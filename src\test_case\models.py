"""
测试用例生成模块的数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class RiskLevel(str, Enum):
    """风险等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class TestPriority(str, Enum):
    """测试优先级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class TestCaseType(str, Enum):
    """测试用例类型枚举"""
    FUNCTIONAL = "functional"
    BOUNDARY = "boundary"
    EXCEPTION = "exception"
    SECURITY = "security"
    PERFORMANCE = "performance"
    INTEGRATION = "integration"


class CryptoRequirement(BaseModel):
    """加密需求"""
    enabled: bool = Field(default=False, description="是否启用加密")
    algorithm: Optional[str] = Field(None, description="加密算法")
    key_management: Optional[str] = Field(None, description="密钥管理方式")
    signature_required: bool = Field(default=False, description="是否需要签名")
    verification_required: bool = Field(default=False, description="是否需要验签")


class TestCaseDefinition(BaseModel):
    """测试用例定义"""
    id: UUID = Field(default_factory=uuid4, description="测试用例ID")
    title: str = Field(..., description="测试用例标题")
    description: str = Field(default="", description="测试用例描述")
    type: str = Field(default="functional", description="测试类型")
    priority: str = Field(default="medium", description="优先级")
    tags: List[str] = Field(default_factory=list, description="标签")
    steps: List[str] = Field(default_factory=list, description="测试步骤")
    expected_result: str = Field(default="", description="预期结果")
    test_data: Dict[str, Any] = Field(default_factory=dict, description="测试数据")
    crypto_requirements: Optional[CryptoRequirement] = Field(None, description="加密需求")
    
    # 向后兼容的字段
    scenario: Optional[str] = Field(None, description="测试场景")
    preconditions: List[str] = Field(default_factory=list, description="前置条件")
    test_steps: List[str] = Field(default_factory=list, description="测试步骤（兼容）")
    expected_results: List[str] = Field(default_factory=list, description="预期结果（兼容）")
    risk_level: str = Field(default="medium", description="风险等级")
    
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class RiskAssessment(BaseModel):
    """风险评估"""
    risk_level: RiskLevel = Field(..., description="风险等级")
    risk_factors: List[str] = Field(default_factory=list, description="风险因素")
    mitigation_strategies: List[str] = Field(default_factory=list, description="缓解策略")
    probability: float = Field(ge=0.0, le=1.0, description="发生概率")
    impact: float = Field(ge=0.0, le=1.0, description="影响程度")
    
    
class GenerationContext(BaseModel):
    """生成上下文"""
    session_id: Optional[UUID] = Field(None, description="会话ID")
    domain: Optional[str] = Field(None, description="业务域")
    system_type: Optional[str] = Field(None, description="系统类型")
    technology_stack: List[str] = Field(default_factory=list, description="技术栈")
    security_requirements: Dict[str, Any] = Field(default_factory=dict, description="安全需求")
    performance_requirements: Dict[str, Any] = Field(default_factory=dict, description="性能需求")
    regulatory_compliance: List[str] = Field(default_factory=list, description="合规要求")
    historical_data: Dict[str, Any] = Field(default_factory=dict, description="历史数据")
    crypto_enabled: bool = Field(default=False, description="是否启用加密")
    
    class Config:
        json_encoders = {UUID: str}
    

class TestCaseTemplate(BaseModel):
    """测试用例模板"""
    template_id: UUID = Field(default_factory=uuid4, description="模板ID")
    name: str = Field(..., description="模板名称")
    description: str = Field(..., description="模板描述")
    test_type: TestCaseType = Field(..., description="测试类型")
    steps_template: List[str] = Field(..., description="步骤模板")
    data_template: Dict[str, Any] = Field(default_factory=dict, description="数据模板")
    crypto_template: Optional[Dict[str, Any]] = Field(None, description="加密模板")
    signature_template: Optional[Dict[str, Any]] = Field(None, description="签名模板")
    tags: List[str] = Field(default_factory=list, description="标签")
    applicable_domains: List[str] = Field(default_factory=list, description="适用域")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class GenerationOptions(BaseModel):
    """生成选项"""
    target_coverage: Optional[float] = Field(None, ge=0.0, le=1.0, description="目标覆盖率")
    max_test_cases: int = Field(default=50, ge=1, le=1000, description="最大测试用例数")
    include_boundary_tests: bool = Field(default=True, description="包含边界测试")
    include_exception_tests: bool = Field(default=True, description="包含异常测试")
    include_security_tests: bool = Field(default=False, description="包含安全测试")
    include_performance_tests: bool = Field(default=False, description="包含性能测试")
    crypto_enabled: bool = Field(default=False, description="启用加密测试")
    signature_enabled: bool = Field(default=False, description="启用签名测试")
    risk_assessment_enabled: bool = Field(default=True, description="启用风险评估")
    use_templates: bool = Field(default=True, description="使用模板")
    generate_test_data: bool = Field(default=True, description="生成测试数据")
    

class GenerationMetadata(BaseModel):
    """生成元数据"""
    generation_time: float = Field(..., description="生成耗时(秒)")
    total_cases: int = Field(..., description="总用例数")
    template_used: str = Field(..., description="使用的模板")
    ai_enhanced: bool = Field(..., description="AI增强")
    model_version: str = Field(..., description="模型版本")
    requirements_complexity: str = Field(..., description="需求复杂度")


class TestCaseGenerationResult(BaseModel):
    """测试用例生成结果"""
    test_cases: List[TestCaseDefinition] = Field(..., description="生成的测试用例")
    generation_metadata: GenerationMetadata = Field(..., description="生成元数据")
    risk_assessment: Optional[RiskAssessment] = Field(None, description="风险评估")
    session_id: UUID = Field(..., description="会话ID")
    
    class Config:
        json_encoders = {UUID: str}


class GenerationResult(BaseModel):
    """生成结果"""
    success: bool = Field(..., description="是否成功")
    test_case_count: int = Field(default=0, description="生成的测试用例数量")
    generation_time: float = Field(..., description="生成耗时(秒)")
    coverage_estimate: Optional[float] = Field(None, description="覆盖率估算")
    risk_summary: Optional[Dict[str, Any]] = Field(None, description="风险摘要")
    warnings: List[str] = Field(default_factory=list, description="警告信息")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="生成元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()} 