---
description: 
globs: 
alwaysApply: true
---
---
title: 项目规则
author: 团队名称或负责人
updated: 2025-06-21
---

# 项目规则

本文件定义本项目开发流程、工具规范、质量保障、安全要求、版本管理、文档与培训等规则，便于团队统一执行、Cursor 解析与自动化校验。

## 目录
- [1. 开发环境与工具](#1-开发环境与工具)
- [2. 依赖与包管理规范](#2-依赖与包管理规范)
- [3. 代码规范与质量保障](#3-代码规范与质量保障)
- [4. 安全与密钥管理](#4-安全与密钥管理)
- [5. 加密签名集成流程](#5-加密签名集成流程)
- [6. 脚本生成与模板管理](#6-脚本生成与模板管理)
- [7. CI/CD 规范](#7-cicd-规范)
- [8. 监控与告警规范](#8-监控与告警规范)
- [9. 发布与版本管理](#9-发布与版本管理)
- [10. 文档与培训规范](#10-文档与培训规范)
- [11. 反馈与持续优化](#11-反馈与持续优化)
- [12. 里程碑与迭代计划](#12-里程碑与迭代计划)
- [13. 风险与防范措施](#13-风险与防范措施)

---

## 1. 开发环境与工具

### 1.1 Python 版本与虚拟环境
- 统一指定 Python 主版本（例如 3.x）的最小兼容版本，在项目配置（如配置文件或环境变量）中声明。
- 使用内部 CLI（uv）或统一工具创建、激活虚拟环境；团队成员须按照同一方式初始化环境，确保依赖隔离。
- Cursor 可通过项目配置或 manifest 文件检测 Python 版本与环境要求。

### 1.2 依赖管理工具
- 依赖管理通过自定义 CLI（uv）完成，支持添加、移除、更新、安装、锁定与安全检查命令。
- 明确区分开发依赖、测试依赖与生产依赖；在配置文件中标识依赖类型，并在 CI 中根据环境安装对应依赖集。
- 依赖清单以结构化文件形式存储，Cursor 可用于检查依赖声明格式与一致性。

### 1.3 编辑与格式化流程
- 选定格式化工具（如 Black、isort 等）和静态检查工具（如 flake8、mypy 可选）；通过预提交钩子或本地命令强制执行。
- 在项目根定义格式化与检查命令入口，Cursor 可解析并提示未遵循规则的文件位置。
- 强制在 CI 环境中运行格式化／静态检查，不符合要求时阻断合并。

### 1.4 异步与框架
- 核心服务使用异步框架（例如 FastAPI + asyncio），避免阻塞；耗时或批量任务通过异步或消息队列处理。
- 在文档中说明异步调用规范、超时与重试策略，Cursor 可提取文档关键字段以生成检查项。

### 1.5 版本控制与提交规范
- 采用 Git 分支模型（如 Git Flow 或 trunk-based），在文档中说明分支命名、合并策略、PR 审查流程。
- 提交信息参照 Conventional Commits 规范：类型、范围、描述结构化，便于自动生成变更日志。
- 在项目根或配置中定义提交模板或校验脚本，Cursor 可检测提交信息格式是否符合要求。

### 1.6 项目初始化脚手架
- 提供命令行初始化脚手架：自动生成目录结构、基础配置、示例配置文件（无示例代码，仅占位文件）。
- 生成的脚手架需包含 docs/、src/、tests/、CI/CD 配置目录、项目规则文件等空模板或占位说明。
- Cursor 可在初始化后扫描并确认必备文件存在或提醒缺失。

---

## 2. 依赖与包管理规范

### 2.1 包管理命令说明
- 在文档中列出 CLI 命令（如 `uv add <包名>`, `uv remove <包名>`, `uv update <包名>`, `uv install`）的功能描述与使用说明，不包含代码示例，仅自然语言说明命令效果。
- 说明开发环境与生产环境依赖的差异流程：如开发阶段安装全部依赖，生产阶段仅安装生产依赖列表。

### 2.2 依赖分组与环境隔离
- 定义依赖分类：core、crypto、测试、部署工具等；在依赖清单或配置中标注分类字段，Cursor 可解析分类信息。
- 文档说明如何基于环境变量或配置文件区分不同环境（开发、测试、预发布、生产），并加载对应依赖或功能开关。

### 2.3 安全检查流程
- 集成依赖漏洞扫描流程：在文档中描述触发方式（本地命令、预提交钩子、CI 阶段）、扫描工具类型、报告处理原则。
- 说明当检测到高危漏洞时的处理流程：升级依赖、评估兼容性、回滚或暂缓发布的决策步骤。

---

## 3. 代码规范与质量保障

### 3.1 格式化与静态检查
- 规定使用的格式化工具、静态分析工具及运行时机（本地、预提交、CI）。
- 文档中描述如何在不同阶段触发检查，以及不通过时的阻断或警告策略。

### 3.2 类型注解与检查
- 可选或推荐在核心组件或关键函数中添加类型注解；文档说明类型注解的书写规范与作用范围。
- 结合静态类型检查工具（mypy 等），在文档中说明如何配置检查级别及忽略规则，不包含示例代码。

### 3.3 日志与监控埋点规范
- 统一日志格式（结构化 JSON），字段包括：timestamp、请求 ID、模块名称、操作类型、状态、耗时、trace 信息等，避免记录敏感明文。
- 文档说明在各模块调用加密/签名时，如何记录元信息（如 keyId、算法名称、但不记录密钥本身）；Cursor 可提取日志字段规范以生成校验列表。
- 监控埋点说明：定义关键操作点、性能指标采集点、异常事件捕获点；文档描述如何标注日志中对应字段，方便监控系统集成。

### 3.4 异常处理与重试
- 文档说明异常分类（可重试 vs 不可重试），重试策略（如指数退避）、降级方案和错误上报流程。
- 在项目规则中列出错误处理要点：捕获范围、记录方式、告警触发条件、与调用方协商重试或失败后流程。
- Cursor 可解析异常处理规范文本，提示开发时需对接相关模块或工具。

---

## 4. 安全与密钥管理

### 4.1 密钥存储与访问
- 文档说明 Vault/KMS 集成思路：运行时通过环境或 Vault 客户端安全获取密钥；禁止将明文密钥写入代码或配置文件。
- 说明开发环境模拟方式：本地模拟 Vault 或使用临时凭证；CI 环境取得访问令牌的思路与步骤描述（不含具体命令）。
- 访问控制原则：最小权限、审计日志、密钥轮换策略。

### 4.2 加密签名策略
- 在项目规则中定义默认算法集与可选算法（如 AES-GCM、RSA2、SM 系列等）；文档说明在不同场景下的选择原则、兼容性注意事项。
- 说明如何在接口规范中标注加密/签名需求或使用独立配置文件；文档描述字段含义及验证流程，不包含示例。
- 描述密钥轮换对旧数据解密兼容的策略与流程，以及在生成脚本或运行时对新旧密钥的支持说明。

### 4.3 配置管理
- 敏感配置通过环境变量或 Vault 动态加载，文档说明配置模型校验流程、必填字段检查、值格式要求。
- 说明是否使用配置中心或配置文件模板，Cursor 可解析配置说明并生成校验脚本或提示。

### 4.4 安全扫描与审计
- 文档说明集成静态安全扫描、第三方库安全审计、依赖漏洞扫描在本地或 CI 中的触发方式与处理机制。
- 审计日志设计：记录重要操作（模型调用、加密签名操作、密钥访问、测试执行发起、缺陷创建等），并说明日志保护措施（如签名或哈希链）、存储与查询流程。
- 合规流程：定期算法评审、依赖更新审查、安全策略复核频率与流程。

---

## 5. 加密签名集成流程

### 5.1 接口加密需求配置
- 文档说明如何在接口规范中标注加密需求：定义配置字段或独立配置文档中说明字段含义、类型和验证规则，不包含示例代码。
- Cursor 可提取该配置说明，用于后续流程自动检测接口需加密或签名场景。

### 5.2 初始化与验证流程
- 说明 CryptoClient 初始化逻辑：如何获取配置、校验环境变量或 Vault 凭证、验证协议一致性；文档中描述初始化顺序与校验点。
- 描述环境校验流程：在本地和 CI 环境如何验证加密协议一致性、keyId 可用性，不包含具体命令，仅流程说明。

### 5.3 测试脚本生成流程
- 文档说明在生成测试脚本过程中，应插入的加密签名调用步骤及异常处理策略：标注占位逻辑和调用接口，如何在模板中体现占位，不含实际代码示例。
- 说明如何处理加解密失败：在脚本流程说明中列出重试或降级方案的判断条件和流程分支描述。

### 5.4 Mock 与本地验证流程
- 文档中说明如何在本地或 CI 环境模拟 Crypto 服务或使用 Mock：包括配置替换、模拟密钥或模拟服务响应的流程描述，不包含命令或示例。
- 说明验证流程一致性：如何校验加密/解密流程在模拟与真实环境中的一致性校验思路和事件审查点。

---

## 6. 脚本生成与模板管理

### 6.1 模板结构与管理
- 文档说明模板的组织方式（例如按语言、按场景分类），以及占位逻辑说明；不包含示例内容，仅描述如何管理和定位模板。
- 提示 Cursor 可依据文档扫描模板目录结构并校验命名或占位字段一致性。

### 6.2 Prompt 片段管理
- 说明 Prompt 片段按功能分类（需求解析、用例生成、脚本生成、加密流程、异常处理等），并规定片段描述格式、占位字段说明与上下文注入方式，不含示例文本。
- 提供 Prompt 校验流程：文档描述校验工具思路，如何检查片段占位字段与实际配置或接口元数据一致。

### 6.3 脚本生成流程说明
- 文档指定从结构化用例定义到脚本文件生成的整体流程：如何加载用例定义、选择模板、替换占位符、输出文件、记录元信息；仅流程描述。
- 说明本地验证流程：文档描述如何执行生成后的脚本进行验证，包括依赖环境检查、配置注入说明，不含示例命令。

### 6.4 本地验证与 Mock 集成
- 说明如何在本地环境或 CI 中结合 Mock 服务进行脚本验证：描述 Mock 服务配置获取、环境变量替换、日志校验思路，无具体命令。
- 文档描述验证结果评估标准，如日志正常、签名通过、响应格式符合预期等。

---

## 7. CI/CD 规范

### 7.1 流程阶段说明
- 文档说明各阶段职责：  
  - 代码检查（格式化、静态分析、安全扫描）  
  - 依赖安装与安全检查  
  - 单元与集成测试（包括 Crypto 模拟测试）  
  - 构建与打包（或部署工件生成）  
  - 部署测试环境（包含 Orchestrator 与 Crypto 服务模拟）  
  - 集成测试（含安全流程验证）  
  - 报告归档与通知（测试结果、安全扫描报告、覆盖率等）  
  - 发布与回滚流程说明  
- Cursor 可解析阶段列表并生成自动化流程校验脚本提示。

### 7.2 Secrets 管理
- 文档说明在 CI 环境如何安全获取 Vault 访问令牌或临时凭证：描述凭证注入思路、生命周期管理，不包含平台具体配置示例。
- 说明处理敏感配置的流程：在 CI 中加载方式、加密存储或临时变量使用原则、审计日志要求。

### 7.3 环境隔离与审批流程
- 文档描述不同环境（开发、测试、预发布、生产）的隔离原则与审查流程：如谁有审批权限、如何触发环境切换、回滚审批条件等。
- 说明发布审批流程中的质量门槛：测试覆盖、安全扫描结果、性能指标等必备条件。

---

## 8. 监控与告警规范

### 8.1 监控指标类别
- 文档列举关键指标分类：  
  - Orchestrator 调用延迟、大模型调用次数与耗时  
  - Crypto 服务延迟与错误率  
  - 脚本执行队列长度、并发资源使用、失败率  
  - 系统资源指标（CPU、内存、磁盘、网络）  
  - 安全相关指标（异常签名失败次数、凭证错误等）  
- 说明指标采集思路：日志埋点、指标上报方式、采集频率等。

### 8.2 日志收集与存储
- 文档描述集中化日志方案：日志聚合平台接入方式、日志保留策略、敏感字段脱敏原则、加密存储或访问控制要求。
- 说明日志备份与清理周期，以及日志查询或审计流程。

### 8.3 告警规则设计
- 文档说明常见告警场景：延迟阈值超限、错误率上升、资源耗尽、密钥失效、CI 流程失败等。
- 说明告警级别分类、通知渠道（Slack/Teams/邮件）、告警处理流程与响应时限。
- Cursor 可提取告警规则文本，用于生成或校验告警配置模板。

---

## 9. 发布与版本管理

### 9.1 版本号与发布策略
- 文档说明采用语义化版本（SemVer）或其他版本方案，描述版本号组成与递增规则。
- 说明发布流程：准备发布说明、变更日志生成、标签打点、文档同步、团队通知等步骤。

### 9.2 回滚与兼容性
- 文档描述回滚流程：触发条件、回滚操作步骤、兼容性验证（如新旧版本数据或配置兼容性检查）。
- 说明前置检查与回滚审批流程，确保在出现问题时能快速、安全地恢复。

### 9.3 发布通知与文档同步
- 文档说明发布后通知范围（开发团队、运维团队、相关业务方），以及文档（API 文档、用户指南、运维手册等）更新流程。
- 说明如何在文档仓库同步更新版本信息，并标注发布时间与变更点。

---

## 10. 文档与培训规范

### 10.1 文档结构与格式
- 采用结构化 Markdown 规范：明确标题层级、段落简洁、关键项使用列表或表格说明；Cursor 友好前提下，可使用 YAML frontmatter 提供元信息（如 title、author、updated）。
- 文档文件组织规则：docs/ 下按模块或功能划分子目录，文件命名与标题一致；维护索引文件（如 SUMMARY.md）体现文档导航结构。
- 说明文档更新流程：变更时需更新元信息（updated 字段）、记录变更原因与影响范围。

### 10.2 示例与教程
- 文档中可引用示例项目或教程，但本项目规则文件不包含示例代码；若另建教程文档，则在该文档中以流程描述展开示例场景，不直接嵌入代码。
- 说明教程内容类型（文本说明、流程图、示意图等），以及更新与审核机制。

### 10.3 培训与支持
- 文档说明培训计划：新成员入职培训、规则宣讲、实践演练；反馈渠道设置：Issue、讨论组等。
- 说明定期回顾与更新培训内容的流程，确保团队理解并遵循项目规则。

---

## 11. 反馈与持续优化

### 11.1 反馈收集流程
- 通过 Issue 管理或专门反馈接口收集对生成逻辑、模板、流程、文档的反馈；文档说明反馈格式（标签、描述、优先级等）。
- 记录反馈元信息：来源、影响范围、建议方案等。

### 11.2 优化流程
- 文档描述基于反馈更新 Prompt 片段或触发模型微调（或提示策略调整）的标准流程：包括评估影响、测试验证、上线步骤、回归验证等。
- 说明流程中需保留变更记录与审计日志，确保可追溯。

### 11.3 知识库管理
- 文档说明经验条目结构：包含场景描述、解决方案、注意事项、适用范围等；存储与检索方式：如在向量数据库或知识库系统中管理条目元信息。
- 生命周期管理：定期审查和清理过时条目；新增或更新时需记录来源与验证情况。

### 11.4 跨项目迁移说明
- 说明如何使用项目元信息（领域、技术栈、安全需求等）匹配历史经验；文档描述迁移流程：筛选、验证、适配、落地评估。
- Cursor 可解析元信息字段，辅助匹配与推荐历史经验条目。

---

## 12. 里程碑与迭代计划

### 12.1 MVP 及阶段目标
- 文档说明各阶段核心目标及可交付成果：MVP 功能范围、关键性能指标、基础安全合规验证等；无具体时间，仅流程和内容说明。
- 描述阶段评审要点：需达到哪些质量标准、测试覆盖级别、监控指标成熟度。

### 12.2 迭代节奏与评审
- 文档说明迭代周期建议（如 Sprint 周期或轻量迭代周期），各迭代包含需求确认、开发实现、测试验证、反馈评审、文档更新等流程步骤。
- 说明评审流程：代码评审、测试结果评审、安全评审、发布准备评审等，明确参与角色与输出物。

---

## 13. 风险与防范措施

### 13.1 大模型依赖风险
- 说明大模型调用可能带来的延迟、成本与隐私风险：在文档中描述预防策略（缓存、限流、降级方案、调用隔离等）。
- 提出成本优化思路：批量请求、缓存常见结果、异步调用与排队机制；文档说明如何评估调用成本与监控支出。
- 隐私合规：在调用前脱敏处理流程、审计记录、调用日志保留周期与访问权限控制。

### 13.2 加密兼容风险
- 描述多种加密算法或协议版本间兼容性验证流程：文档说明协议一致性校验、版本协商策略、兼容性测试思路。
- 性能优化：对于高并发加密操作说明并行或批处理思路、硬件加速或异步调用方案；文档记录评估指标和优化反馈流程。

### 13.3 环境配置风险
- 说明本地模拟、CI 预检测流程：文档描述如何在提交或 CI 初期阶段检测环境差异、配置缺失或不一致问题。
- 提出防范措施：环境检查脚本或文档提示、容器化隔离、模拟凭证校验、自动化环境健康检查步骤说明。

### 13.4 团队接受风险
- 文档描述培训与支持方案：如何让团队理解 AI Agent 与加密集成带来的变革；提供示例场景演示流程，但不嵌入代码。
- 说明反馈与改进机制：项目初期设立试点团队、收集使用体验、持续优化流程与文档，确保平滑过渡。

---

## 附录：Cursor 友好格式说明

- 文档使用结构化 Markdown，明确标题层级（`#`、`##`、`###` 等），列表和表格（仅在需要结构化展示字段时使用）。
- 文件组织：每个大模块或规范单独文档（例如 docs/project_rules.md 专注规则），并在索引文件（如 SUMMARY.md）中注册；Cursor 可自动提取目录结构和标题层级。
- 文档内容以说明性文本代替示例代码；占位逻辑和流程说明使用自然语言表述关键步骤与校验点。
- 可选 YAML frontmatter 提供元信息（title、author、updated），便于版本管理与自动化处理。
- 变更同步：当规则或流程更新时，及时更新元信息并在文档中明确变更原因与影响范围，以便 Cursor 触发相应校验或提醒。

---

## 下一步行动指南
- 在项目根运行初始化脚手架命令，生成空模板文件：docs/project_rules.md、docs/templates/、依赖清单、CI/CD 占位文件等。
- 团队评审本规则文档，确认内容完整性和可操作性；在 Cursor 中验证文档可解析性，补充必要元信息字段。
- 基于规则开发或调整内部工具（uv CLI 扩展、Prompt 校验工具、依赖扫描脚本等），并在 CI 中集成。
- 持续迭代：根据实践反馈不断完善规则、文档和自动化校验能力，确保团队高效一致地交付高质量、安全合规的 AI Agent 项目。


