#!/usr/bin/env python3
"""
简单的models测试
"""

def test_basic_imports():
    """测试基础导入"""
    try:
        print("Testing basic imports...")
        
        print("1. Testing standard library imports...")
        from datetime import datetime
        from enum import Enum
        from typing import Dict, List, Optional, Any
        from uuid import UUID, uuid4
        print("   ✓ Standard library imports OK")
        
        print("2. Testing pydantic import...")
        from pydantic import BaseModel, Field
        print("   ✓ Pydantic import OK")
        
        print("3. Testing simple model creation...")
        class SimpleModel(BaseModel):
            name: str = Field(..., description="名称")
            value: int = Field(default=0, description="值")
        
        model = SimpleModel(name="test")
        print(f"   ✓ Simple model created: {model}")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic imports failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_enum_creation():
    """测试枚举创建"""
    try:
        print("\n4. Testing enum creation...")
        from enum import Enum
        
        class TestStatus(str, Enum):
            PENDING = "pending"
            RUNNING = "running"
            COMPLETED = "completed"
        
        status = TestStatus.PENDING
        print(f"   ✓ Enum created: {status}")
        
        return True
        
    except Exception as e:
        print(f"✗ Enum creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_models_import():
    """测试models文件导入"""
    try:
        print("\n5. Testing models file import...")
        
        # 尝试逐个导入模型
        from src.orchestrator.models import TaskStatus
        print("   ✓ TaskStatus imported")
        
        from src.orchestrator.models import ExecutionState
        print("   ✓ ExecutionState imported")
        
        from src.orchestrator.models import SessionContext
        print("   ✓ SessionContext imported")
        
        # 测试创建实例
        session = SessionContext()
        print(f"   ✓ SessionContext instance created: {session.session_id}")
        
        return True
        
    except Exception as e:
        print(f"✗ Models import failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Simple Models Test")
    print("=" * 30)
    
    success = True
    
    success &= test_basic_imports()
    success &= test_enum_creation()
    success &= test_models_import()
    
    if success:
        print("\n✓ All tests passed!")
    else:
        print("\n✗ Some tests failed!") 