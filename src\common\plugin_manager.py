"""
插件管理器

负责插件的加载、管理和生命周期控制
支持动态插件加载、依赖管理和安全隔离
"""

import os
import sys
import importlib
import importlib.util
from typing import Dict, List, Optional, Any, Type
from pathlib import Path
import asyncio
from abc import ABC, abstractmethod
import json
from dataclasses import dataclass
from enum import Enum

from src.common.logger import get_logger

logger = get_logger(__name__)


class PluginState(str, Enum):
    """插件状态"""
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    ACTIVE = "active"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class PluginMetadata:
    """插件元数据"""
    name: str
    version: str
    description: str
    author: str
    dependencies: List[str]
    entry_point: str
    config_schema: Optional[Dict[str, Any]] = None
    min_system_version: Optional[str] = None
    max_system_version: Optional[str] = None


class PluginInterface(ABC):
    """插件接口基类"""
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        pass
    
    @abstractmethod
    async def activate(self) -> bool:
        """激活插件"""
        pass
    
    @abstractmethod
    async def deactivate(self) -> bool:
        """停用插件"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> bool:
        """清理插件资源"""
        pass
    
    @property
    @abstractmethod
    def metadata(self) -> PluginMetadata:
        """获取插件元数据"""
        pass


class PluginInfo:
    """插件信息"""
    
    def __init__(self, metadata: PluginMetadata, plugin_path: Path):
        self.metadata = metadata
        self.plugin_path = plugin_path
        self.state = PluginState.UNLOADED
        self.instance: Optional[PluginInterface] = None
        self.module = None
        self.error_message: Optional[str] = None
        self.load_time: Optional[float] = None


class PluginManager:
    """
    插件管理器
    
    负责插件的发现、加载、管理和生命周期控制
    """
    
    def __init__(self, plugin_directory: str = "src/plugins"):
        self.logger = get_logger(__name__)
        self.plugin_directory = Path(plugin_directory)
        self.plugins: Dict[str, PluginInfo] = {}
        self.active_plugins: Dict[str, PluginInterface] = {}
        self.plugin_hooks: Dict[str, List[callable]] = {}
        self._initialized = False
        
    async def initialize(self) -> None:
        """初始化插件管理器"""
        try:
            self.logger.info("Initializing PluginManager")
            
            # 确保插件目录存在
            self.plugin_directory.mkdir(parents=True, exist_ok=True)
            
            # 发现插件
            await self._discover_plugins()
            
            self._initialized = True
            self.logger.info(f"PluginManager initialized with {len(self.plugins)} plugins discovered")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize PluginManager: {e}")
            raise
    
    async def load_plugins(self) -> None:
        """加载所有发现的插件"""
        if not self._initialized:
            raise RuntimeError("PluginManager not initialized")
        
        try:
            self.logger.info("Loading plugins")
            
            # 按依赖顺序加载插件
            load_order = self._resolve_dependencies()
            
            for plugin_name in load_order:
                await self._load_plugin(plugin_name)
            
            self.logger.info(f"Loaded {len(self.active_plugins)} plugins successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load plugins: {e}")
            raise
    
    async def unload_plugins(self) -> None:
        """卸载所有插件"""
        try:
            self.logger.info("Unloading plugins")
            
            # 反向顺序卸载插件
            for plugin_name in reversed(list(self.active_plugins.keys())):
                await self._unload_plugin(plugin_name)
            
            self.logger.info("All plugins unloaded")
            
        except Exception as e:
            self.logger.error(f"Failed to unload plugins: {e}")
    
    async def reload_plugin(self, plugin_name: str) -> bool:
        """重新加载指定插件"""
        try:
            if plugin_name in self.active_plugins:
                await self._unload_plugin(plugin_name)
            
            return await self._load_plugin(plugin_name)
            
        except Exception as e:
            self.logger.error(f"Failed to reload plugin {plugin_name}: {e}")
            return False
    
    def get_plugin(self, plugin_name: str) -> Optional[PluginInterface]:
        """获取插件实例"""
        return self.active_plugins.get(plugin_name)
    
    def get_plugin_info(self, plugin_name: str) -> Optional[PluginInfo]:
        """获取插件信息"""
        return self.plugins.get(plugin_name)
    
    def list_plugins(self) -> List[str]:
        """列出所有插件名称"""
        return list(self.plugins.keys())
    
    def list_active_plugins(self) -> List[str]:
        """列出所有活跃插件名称"""
        return list(self.active_plugins.keys())
    
    async def _discover_plugins(self) -> None:
        """发现插件"""
        try:
            if not self.plugin_directory.exists():
                self.logger.warning(f"Plugin directory {self.plugin_directory} does not exist")
                return
            
            # 扫描插件目录
            for plugin_path in self.plugin_directory.iterdir():
                if plugin_path.is_dir() and not plugin_path.name.startswith('.'):
                    await self._discover_plugin(plugin_path)
            
            self.logger.info(f"Discovered {len(self.plugins)} plugins")
            
        except Exception as e:
            self.logger.error(f"Failed to discover plugins: {e}")
            raise
    
    async def _discover_plugin(self, plugin_path: Path) -> None:
        """发现单个插件"""
        try:
            # 查找插件元数据文件
            metadata_file = plugin_path / "plugin.json"
            if not metadata_file.exists():
                self.logger.debug(f"No plugin.json found in {plugin_path}")
                return
            
            # 加载元数据
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata_dict = json.load(f)
            
            metadata = PluginMetadata(**metadata_dict)
            
            # 验证入口点文件存在
            entry_file = plugin_path / f"{metadata.entry_point}.py"
            if not entry_file.exists():
                self.logger.warning(f"Entry point {entry_file} not found for plugin {metadata.name}")
                return
            
            # 创建插件信息
            plugin_info = PluginInfo(metadata, plugin_path)
            self.plugins[metadata.name] = plugin_info
            
            self.logger.debug(f"Discovered plugin: {metadata.name} v{metadata.version}")
            
        except Exception as e:
            self.logger.error(f"Failed to discover plugin in {plugin_path}: {e}")
    
    def _resolve_dependencies(self) -> List[str]:
        """解析插件依赖关系，返回加载顺序"""
        try:
            # 简单的拓扑排序实现
            visited = set()
            temp_visited = set()
            result = []
            
            def visit(plugin_name: str):
                if plugin_name in temp_visited:
                    raise ValueError(f"Circular dependency detected involving {plugin_name}")
                
                if plugin_name in visited:
                    return
                
                temp_visited.add(plugin_name)
                
                plugin_info = self.plugins.get(plugin_name)
                if plugin_info:
                    for dep in plugin_info.metadata.dependencies:
                        if dep in self.plugins:
                            visit(dep)
                        else:
                            self.logger.warning(f"Dependency {dep} not found for plugin {plugin_name}")
                
                temp_visited.remove(plugin_name)
                visited.add(plugin_name)
                result.append(plugin_name)
            
            for plugin_name in self.plugins:
                visit(plugin_name)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to resolve dependencies: {e}")
            # 返回简单的字母顺序作为后备
            return sorted(self.plugins.keys())
    
    async def _load_plugin(self, plugin_name: str) -> bool:
        """加载单个插件"""
        try:
            plugin_info = self.plugins.get(plugin_name)
            if not plugin_info:
                self.logger.error(f"Plugin {plugin_name} not found")
                return False
            
            if plugin_info.state == PluginState.ACTIVE:
                self.logger.debug(f"Plugin {plugin_name} already active")
                return True
            
            self.logger.info(f"Loading plugin: {plugin_name}")
            plugin_info.state = PluginState.LOADING
            
            # 动态导入插件模块
            spec = importlib.util.spec_from_file_location(
                plugin_name,
                plugin_info.plugin_path / f"{plugin_info.metadata.entry_point}.py"
            )
            
            if spec is None or spec.loader is None:
                raise ImportError(f"Cannot load plugin module for {plugin_name}")
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找插件类
            plugin_class = None
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    issubclass(attr, PluginInterface) and 
                    attr != PluginInterface):
                    plugin_class = attr
                    break
            
            if plugin_class is None:
                raise ImportError(f"No PluginInterface implementation found in {plugin_name}")
            
            # 创建插件实例
            plugin_instance = plugin_class()
            
            # 初始化插件
            config = {}  # 这里可以从配置文件加载插件配置
            if await plugin_instance.initialize(config):
                if await plugin_instance.activate():
                    plugin_info.instance = plugin_instance
                    plugin_info.module = module
                    plugin_info.state = PluginState.ACTIVE
                    self.active_plugins[plugin_name] = plugin_instance
                    
                    self.logger.info(f"Plugin {plugin_name} loaded and activated successfully")
                    return True
                else:
                    await plugin_instance.cleanup()
                    plugin_info.state = PluginState.ERROR
                    plugin_info.error_message = "Failed to activate plugin"
            else:
                plugin_info.state = PluginState.ERROR
                plugin_info.error_message = "Failed to initialize plugin"
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to load plugin {plugin_name}: {e}")
            if plugin_name in self.plugins:
                self.plugins[plugin_name].state = PluginState.ERROR
                self.plugins[plugin_name].error_message = str(e)
            return False
    
    async def _unload_plugin(self, plugin_name: str) -> bool:
        """卸载单个插件"""
        try:
            plugin_instance = self.active_plugins.get(plugin_name)
            if not plugin_instance:
                return True
            
            self.logger.info(f"Unloading plugin: {plugin_name}")
            
            # 停用并清理插件
            await plugin_instance.deactivate()
            await plugin_instance.cleanup()
            
            # 从活跃插件中移除
            del self.active_plugins[plugin_name]
            
            # 更新插件状态
            if plugin_name in self.plugins:
                self.plugins[plugin_name].state = PluginState.LOADED
                self.plugins[plugin_name].instance = None
            
            self.logger.info(f"Plugin {plugin_name} unloaded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to unload plugin {plugin_name}: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取插件管理器统计信息"""
        states = {}
        for plugin_info in self.plugins.values():
            state = plugin_info.state.value
            states[state] = states.get(state, 0) + 1
        
        return {
            "total_plugins": len(self.plugins),
            "active_plugins": len(self.active_plugins),
            "plugin_states": states,
            "plugin_directory": str(self.plugin_directory)
        }
