"""
TestGenius 加密模块综合测试套件

测试覆盖：
1. 加密/解密功能测试
2. 签名/验签功能测试
3. 哈希计算测试
4. 密钥管理测试
5. 错误处理和边界条件测试
6. 性能和安全测试
"""

import pytest
import asyncio
import base64
import os
from typing import Dict, Any
from unittest.mock import MagicMock, patch
from uuid import uuid4

from src.crypto.client import CryptoClient
from src.crypto.models import (
    CryptoRequest,
    CryptoResponse,
    CryptoOperation,
    EncryptionConfig,
    SignatureConfig,
    KeyConfig,
    KeyType,
    EncryptionAlgorithm,
    SignatureAlgorithm,
    HashAlgorithm,
    VaultConfig,
)


class TestCryptoClientCore:
    """加密客户端核心功能测试"""
    
    @pytest.fixture
    async def crypto_client(self):
        """创建加密客户端"""
        client = CryptoClient(mode="dev")
        await client.initialize()
        yield client
        await client.cleanup()
    
    @pytest.mark.asyncio
    async def test_client_initialization(self, crypto_client):
        """测试客户端初始化"""
        assert crypto_client._initialized is True
        assert crypto_client._mode == "dev"
        assert crypto_client._stats is not None
        assert isinstance(crypto_client._key_cache, dict)
    
    @pytest.mark.asyncio
    async def test_aes_gcm_encryption_decryption(self, crypto_client):
        """测试AES-GCM加密解密"""
        original_text = "这是一个测试消息"
        key_config = KeyConfig(
            key_id="test-aes-256-gcm-key",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_size=256
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )
        
        # 加密
        encrypt_request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data=original_text,
            encryption_config=encryption_config
        )
        
        encrypt_response = await crypto_client.process_request(encrypt_request)
        
        assert encrypt_response.success is True
        assert encrypt_response.result is not None
        assert encrypt_response.result != original_text
        
        # 解密
        decrypt_request = CryptoRequest(
            operation=CryptoOperation.DECRYPT,
            data=encrypt_response.result,
            encryption_config=encryption_config
        )
        
        decrypt_response = await crypto_client.process_request(decrypt_request)
        
        assert decrypt_response.success is True
        assert decrypt_response.result == original_text
    
    @pytest.mark.asyncio
    async def test_hash_operations(self, crypto_client):
        """测试哈希计算"""
        test_data = "测试数据"
        
        # 测试SHA256
        sha256_request = CryptoRequest(
            operation=CryptoOperation.HASH,
            data=test_data,
            hash_algorithm=HashAlgorithm.SHA256
        )
        
        sha256_response = await crypto_client.process_request(sha256_request)
        
        assert sha256_response.success is True
        assert sha256_response.result is not None
        assert len(sha256_response.result) == 64  # SHA256输出64个十六进制字符
        
        # 测试MD5
        md5_request = CryptoRequest(
            operation=CryptoOperation.HASH,
            data=test_data,
            hash_algorithm=HashAlgorithm.MD5
        )
        
        md5_response = await crypto_client.process_request(md5_request)
        
        assert md5_response.success is True
        assert md5_response.result is not None
        assert len(md5_response.result) == 32  # MD5输出32个十六进制字符
    
    @pytest.mark.asyncio
    async def test_statistics_tracking(self, crypto_client):
        """测试统计信息跟踪"""
        initial_stats = await crypto_client.get_stats()
        initial_total = initial_stats.total_operations
        
        # 执行一些操作
        request = CryptoRequest(
            operation=CryptoOperation.HASH,
            data="test data",
            hash_algorithm=HashAlgorithm.SHA256
        )
        
        await crypto_client.process_request(request)
        await crypto_client.process_request(request)
        
        # 检查统计信息更新
        updated_stats = await crypto_client.get_stats()
        
        assert updated_stats.total_operations == initial_total + 2
        assert updated_stats.successful_operations >= initial_stats.successful_operations + 2
        assert CryptoOperation.HASH in updated_stats.operations_by_type


class TestCryptoClientBoundaryConditions:
    """边界条件测试"""
    
    @pytest.fixture
    async def crypto_client(self):
        client = CryptoClient(mode="dev")
        await client.initialize()
        yield client
        await client.cleanup()
    
    @pytest.mark.asyncio
    async def test_empty_data_encryption(self, crypto_client):
        """测试空数据加密"""
        key_config = KeyConfig(
            key_id="test-empty-data-key",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )
        
        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data="",  # 空数据
            encryption_config=encryption_config
        )
        
        response = await crypto_client.process_request(request)
        
        # 应该能处理空数据
        assert response.success is True
        assert response.result is not None
    
    @pytest.mark.asyncio
    async def test_large_data_encryption(self, crypto_client):
        """测试大数据加密"""
        large_data = "A" * 10000  # 10KB数据
        
        key_config = KeyConfig(
            key_id="test-large-data-key",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )
        
        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data=large_data,
            encryption_config=encryption_config
        )
        
        response = await crypto_client.process_request(request)
        
        assert response.success is True
        assert response.result is not None
        assert response.processing_time is not None
    
    @pytest.mark.asyncio
    async def test_invalid_key_id(self, crypto_client):
        """测试无效密钥ID"""
        key_config = KeyConfig(
            key_id="short",  # 太短的密钥ID
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )
        
        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data="test data",
            encryption_config=encryption_config
        )
        
        response = await crypto_client.process_request(request)
        
        # 应该失败，因为密钥ID太短
        assert response.success is False
        assert "Key ID must be at least 15 characters" in response.error_message
    
    @pytest.mark.asyncio
    async def test_unsupported_algorithm(self, crypto_client):
        """测试不支持的算法"""
        key_config = KeyConfig(
            key_id="test-unsupported-algorithm-key",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.SM4  # 可能不支持的算法
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.SM4,
            key_config=key_config
        )
        
        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data="test data",
            encryption_config=encryption_config
        )
        
        response = await crypto_client.process_request(request)
        
        # 在开发模式下可能会使用模拟实现
        # 检查是否有适当的处理
        assert response is not None


class TestCryptoClientErrorHandling:
    """错误处理测试"""
    
    @pytest.fixture
    async def crypto_client(self):
        client = CryptoClient(mode="dev")
        await client.initialize()
        yield client
        await client.cleanup()
    
    @pytest.mark.asyncio
    async def test_missing_encryption_config(self, crypto_client):
        """测试缺少加密配置"""
        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data="test data"
            # 缺少encryption_config
        )
        
        response = await crypto_client.process_request(request)
        
        assert response.success is False
        assert "Encryption config is required" in response.error_message
    
    @pytest.mark.asyncio
    async def test_invalid_operation(self, crypto_client):
        """测试无效操作"""
        request = CryptoRequest(
            operation="INVALID_OPERATION",  # 无效操作
            data="test data"
        )
        
        response = await crypto_client.process_request(request)
        
        assert response.success is False
        assert "Unsupported operation" in response.error_message
    
    @pytest.mark.asyncio
    async def test_decryption_with_wrong_data(self, crypto_client):
        """测试用错误数据解密"""
        key_config = KeyConfig(
            key_id="test-wrong-decrypt-key",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )
        
        request = CryptoRequest(
            operation=CryptoOperation.DECRYPT,
            data="invalid_encrypted_data",  # 无效的加密数据
            encryption_config=encryption_config
        )
        
        response = await crypto_client.process_request(request)
        
        assert response.success is False
        assert response.error_message is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
