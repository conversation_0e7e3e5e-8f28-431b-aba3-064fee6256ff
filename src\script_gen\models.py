"""
脚本生成模块数据模型

定义脚本生成相关的数据结构和类型
"""

from typing import Dict, List, Optional, Any
from uuid import UUID
from pydantic import BaseModel, Field
from enum import Enum

from src.orchestrator.models import TestCaseDefinition


class ScriptLanguage(str, Enum):
    """支持的脚本语言"""
    PYTHON = "python"
    JAVA = "java"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    CSHARP = "csharp"
    GO = "go"


class ScriptFramework(str, Enum):
    """支持的测试框架"""
    PYTEST = "pytest"
    UNITTEST = "unittest"
    TESTNG = "testng"
    JUNIT = "junit"
    JEST = "jest"
    MOCHA = "mocha"
    NUNIT = "nunit"
    XUNIT = "xunit"
    GINKGO = "ginkgo"


class ScriptTemplate(BaseModel):
    """脚本模板定义"""
    name: str = Field(..., description="模板名称")
    language: str = Field(..., description="编程语言")
    framework: str = Field(..., description="测试框架")
    content: str = Field(..., description="模板内容")
    version: str = Field(default="1.0.0", description="模板版本")
    dependencies: List[str] = Field(default_factory=list, description="依赖包列表")
    placeholders: List[str] = Field(default_factory=list, description="占位符列表")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")

    class Config:
        json_encoders = {UUID: str}


class TemplateContext(BaseModel):
    """模板渲染上下文"""
    test_case: TestCaseDefinition = Field(..., description="测试用例定义")
    language: str = Field(..., description="目标语言")
    framework: str = Field(..., description="目标框架")
    crypto_enabled: bool = Field(default=False, description="是否启用加密")
    environment: str = Field(default="test", description="执行环境")
    custom_variables: Dict[str, Any] = Field(default_factory=dict, description="自定义变量")

    class Config:
        json_encoders = {UUID: str}


class ScriptGenerationOptions(BaseModel):
    """脚本生成选项"""
    target_language: ScriptLanguage = Field(default=ScriptLanguage.PYTHON, description="目标语言")
    target_framework: ScriptFramework = Field(default=ScriptFramework.PYTEST, description="目标框架")
    include_setup: bool = Field(default=True, description="是否包含设置代码")
    include_teardown: bool = Field(default=True, description="是否包含清理代码")
    include_assertions: bool = Field(default=True, description="是否包含断言")
    include_logging: bool = Field(default=True, description="是否包含日志")
    crypto_enabled: bool = Field(default=False, description="是否启用加密功能")
    parallel_execution: bool = Field(default=False, description="是否支持并行执行")
    timeout_seconds: Optional[int] = Field(None, description="超时时间（秒）")
    custom_imports: List[str] = Field(default_factory=list, description="自定义导入")
    environment_variables: Dict[str, str] = Field(default_factory=dict, description="环境变量")

    class Config:
        json_encoders = {UUID: str}


class ScriptGenerationResult(BaseModel):
    """脚本生成结果"""
    success: bool = Field(..., description="是否成功")
    scripts: List['ScriptDefinition'] = Field(default_factory=list, description="生成的脚本列表")
    total_count: int = Field(default=0, description="总脚本数量")
    successful_count: int = Field(default=0, description="成功生成数量")
    failed_count: int = Field(default=0, description="失败数量")
    generation_time: float = Field(default=0.0, description="生成耗时（秒）")
    errors: List[str] = Field(default_factory=list, description="错误信息列表")
    warnings: List[str] = Field(default_factory=list, description="警告信息列表")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")

    class Config:
        json_encoders = {UUID: str}


class ScriptDefinition(BaseModel):
    """脚本定义"""
    script_id: UUID = Field(..., description="脚本ID")
    test_case_id: UUID = Field(..., description="关联的测试用例ID")
    language: str = Field(..., description="编程语言")
    framework: str = Field(..., description="测试框架")
    content: str = Field(..., description="脚本内容")
    dependencies: List[str] = Field(default_factory=list, description="依赖包列表")
    file_name: Optional[str] = Field(None, description="文件名")
    file_path: Optional[str] = Field(None, description="文件路径")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")

    class Config:
        json_encoders = {UUID: str}


class TemplateValidationResult(BaseModel):
    """模板验证结果"""
    is_valid: bool = Field(..., description="是否有效")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    missing_placeholders: List[str] = Field(default_factory=list, description="缺失的占位符")
    unused_placeholders: List[str] = Field(default_factory=list, description="未使用的占位符")

    class Config:
        json_encoders = {UUID: str}