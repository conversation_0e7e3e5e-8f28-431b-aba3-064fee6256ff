# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml
.pdm-python
.pdm-build/

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~

# 项目特定文件
logs/
data/
temp/
.secrets/
*.key
*.pem
*.p12
*.pfx

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件 (敏感信息)
.env
.env.local
.env.production
config/local.yaml
config/secrets.yaml

# 模型文件
models/
checkpoints/
*.pkl
*.joblib
*.model

# 临时文件
tmp/
cache/
.tmp/
.cache/

# 测试输出
test-results/
test-reports/
screenshots/

# 部署文件
deploy/secrets/
deploy/configs/production/
k8s/secrets/

# 文档生成
docs/build/
docs/dist/

# Rust 构建产物 (Crypto 模块)
target/
Cargo.lock

# Node.js (如果有前端组件)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 备份文件
*.bak
*.backup
*.swp
*.swo

# 压缩文件
*.zip
*.tar.gz
*.rar

# IDE 临时文件
*.tmp
*.temp 