#!/usr/bin/env python3
"""
TestGenius Orchestrator 完整测试脚本

测试 Orchestrator 服务及其集成的模块功能
"""

import asyncio
import json
import time
from typing import Dict, Any
from uuid import uuid4

import httpx

# 测试配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"


async def test_health_checks():
    """测试健康检查端点"""
    print("=" * 50)
    print("测试健康检查端点")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        # 基础健康检查
        response = await client.get(f"{BASE_URL}/health")
        print(f"健康检查: {response.status_code} - {response.json()}")
        
        # 就绪检查
        response = await client.get(f"{BASE_URL}/health/ready")
        print(f"就绪检查: {response.status_code} - {response.json()}")
        
        # 存活检查
        response = await client.get(f"{BASE_URL}/health/live")
        print(f"存活检查: {response.status_code} - {response.json()}")


async def test_session_management():
    """测试会话管理"""
    print("\n" + "=" * 50)
    print("测试会话管理")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        # 创建会话
        response = await client.post(f"{API_BASE}/sessions")
        assert response.status_code == 200
        session_data = response.json()
        session_id = session_data["session_id"]
        print(f"创建会话: {session_id}")
        print(f"会话数据: {json.dumps(session_data, indent=2, ensure_ascii=False)}")
        
        # 获取会话
        response = await client.get(f"{API_BASE}/sessions/{session_id}")
        assert response.status_code == 200
        session_data = response.json()
        print(f"获取会话: {json.dumps(session_data, indent=2, ensure_ascii=False)}")
        
        return session_id


async def test_test_case_generation(session_id: str):
    """测试用例生成测试"""
    print("\n" + "=" * 50)
    print("测试用例生成")
    print("=" * 50)
    
    # 测试请求数据
    test_request = {
        "session_id": session_id,
        "requirement_text": """
        需求描述：
        1. 用户登录系统，支持用户名/密码和手机号/验证码两种登录方式
        2. 登录成功后，用户可以查询个人信息和订单历史
        3. 系统支持用户修改个人信息，包括姓名、邮箱、手机号
        4. 所有敏感数据传输需要加密，用户操作需要数字签名验证
        5. 系统响应时间不超过2秒，支持1000并发用户
        """,
        "context": {
            "domain": "电商系统",
            "system_type": "web_application",
            "technology_stack": ["Python", "FastAPI", "React", "PostgreSQL"],
            "security_requirements": {
                "encryption": True,
                "signature": True,
                "authentication": "JWT"
            }
        },
        "generation_options": {
            "max_test_cases": 15,
            "include_boundary_tests": True,
            "include_exception_tests": True,
            "include_security_tests": True
        },
        "crypto_enabled": True,
        "target_coverage": 0.85
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        # 发送测试用例生成请求
        response = await client.post(
            f"{API_BASE}/test-cases/generate",
            json=test_request
        )
        
        assert response.status_code == 200
        result = response.json()
        
        print(f"生成任务ID: {result['task_id']}")
        print(f"生成的测试用例数量: {len(result['test_cases'])}")
        print(f"生成状态: {result['status']}")
        
        # 显示生成的测试用例
        for i, test_case in enumerate(result['test_cases'], 1):
            print(f"\n--- 测试用例 {i} ---")
            print(f"标题: {test_case['title']}")
            print(f"描述: {test_case['description']}")
            print(f"场景: {test_case['scenario']}")
            print(f"优先级: {test_case['priority']}")
            print(f"风险等级: {test_case['risk_level']}")
            print(f"标签: {', '.join(test_case['tags'])}")
            
            if test_case.get('crypto_requirements'):
                crypto_req = test_case['crypto_requirements']
                print(f"加密需求: 启用={crypto_req['enabled']}, 算法={crypto_req.get('algorithm')}")
            
            if test_case.get('signature_requirements'):
                sign_req = test_case['signature_requirements']
                print(f"签名需求: 启用={sign_req['enabled']}, 算法={sign_req.get('algorithm')}")
        
        return result['test_cases']


async def test_script_generation(session_id: str, test_cases: list):
    """测试脚本生成"""
    print("\n" + "=" * 50)
    print("测试脚本生成")
    print("=" * 50)
    
    # 选择前3个测试用例进行脚本生成
    test_case_ids = [tc['test_case_id'] for tc in test_cases[:3]]
    
    script_request = {
        "session_id": session_id,
        "test_case_ids": test_case_ids,
        "target_language": "python",
        "target_framework": "pytest",
        "generation_options": {
            "include_crypto": True,
            "include_assertions": True,
            "include_logging": True
        }
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        # 发送脚本生成请求
        response = await client.post(
            f"{API_BASE}/scripts/generate",
            json=script_request
        )
        
        assert response.status_code == 200
        result = response.json()
        
        print(f"生成任务ID: {result['task_id']}")
        print(f"生成的脚本数量: {len(result['scripts'])}")
        print(f"生成状态: {result['status']}")
        
        # 显示生成的脚本
        for i, script in enumerate(result['scripts'], 1):
            print(f"\n--- 脚本 {i} ---")
            print(f"脚本ID: {script['script_id']}")
            print(f"测试用例ID: {script['test_case_id']}")
            print(f"语言: {script['language']}")
            print(f"框架: {script['framework']}")
            print(f"脚本内容 (前200字符): {script['content'][:200]}...")
        
        return result['scripts']


async def test_execution(session_id: str, scripts: list):
    """测试执行管理"""
    print("\n" + "=" * 50)
    print("测试执行管理")
    print("=" * 50)
    
    # 选择前2个脚本进行执行
    script_ids = [script['script_id'] for script in scripts[:2]]
    
    execution_request = {
        "session_id": session_id,
        "script_ids": script_ids,
        "execution_options": {
            "timeout": 60,
            "retry_count": 3,
            "environment_config": {
                "crypto_enabled": True,
                "mock_services": True
            }
        },
        "environment": "test",
        "parallel": True,
        "timeout": 120
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        # 启动执行
        response = await client.post(
            f"{API_BASE}/executions",
            json=execution_request
        )
        
        assert response.status_code == 200
        result = response.json()
        execution_id = result['execution_id']
        
        print(f"执行ID: {execution_id}")
        print(f"初始状态: {result['status']}")
        print(f"脚本数量: {result['script_count']}")
        
        # 轮询执行状态
        for attempt in range(10):  # 最多检查10次
            await asyncio.sleep(2)  # 等待2秒
            
            status_response = await client.get(
                f"{API_BASE}/executions/{execution_id}/status"
            )
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"执行状态 (第{attempt+1}次检查): {status_data['status']}")
                print(f"进度: {status_data['progress']:.2%}")
                print(f"已完成: {status_data['completed_scripts']}/{status_data['total_scripts']}")
                
                if status_data['status'] in ['completed', 'failed']:
                    print(f"执行完成，最终状态: {status_data['status']}")
                    
                    # 显示执行结果
                    if status_data.get('results'):
                        for i, result in enumerate(status_data['results'], 1):
                            print(f"\n--- 执行结果 {i} ---")
                            print(f"脚本ID: {result['script_id']}")
                            print(f"状态: {result['status']}")
                            print(f"执行时长: {result.get('duration', 'N/A')}秒")
                            print(f"退出码: {result.get('exit_code', 'N/A')}")
                    
                    break
            else:
                print(f"获取状态失败: {status_response.status_code}")
        
        return execution_id


async def test_crypto_integration():
    """测试加密模块集成"""
    print("\n" + "=" * 50)
    print("测试加密模块集成")
    print("=" * 50)
    
    # 这里可以添加直接的加密模块测试
    # 由于加密模块还未完全集成到API中，我们先跳过
    print("加密模块集成测试 - 待实现")


async def test_feedback_submission(session_id: str):
    """测试反馈提交"""
    print("\n" + "=" * 50)
    print("测试反馈提交")
    print("=" * 50)
    
    feedback_data = {
        "session_id": session_id,
        "feedback_type": "test_case_improvement",
        "content": "生成的测试用例质量很好，建议增加更多边界条件测试",
        "rating": 4,
        "suggestions": [
            "增加空值测试",
            "添加并发测试场景",
            "优化加密算法选择"
        ],
        "metadata": {
            "user_id": "test_user",
            "timestamp": time.time()
        }
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{API_BASE}/feedback",
            json=feedback_data
        )
        
        assert response.status_code == 200
        result = response.json()
        print(f"反馈提交结果: {result}")


async def main():
    """主测试函数"""
    print("TestGenius Orchestrator 完整测试开始")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"服务地址: {BASE_URL}")
    
    try:
        # 1. 健康检查
        await test_health_checks()
        
        # 2. 会话管理
        session_id = await test_session_management()
        
        # 3. 测试用例生成
        test_cases = await test_test_case_generation(session_id)
        
        # 4. 脚本生成
        scripts = await test_script_generation(session_id, test_cases)
        
        # 5. 执行管理
        execution_id = await test_execution(session_id, scripts)
        
        # 6. 加密集成测试
        await test_crypto_integration()
        
        # 7. 反馈提交
        await test_feedback_submission(session_id)
        
        print("\n" + "=" * 50)
        print("所有测试完成!")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 检查服务是否启动
    print("正在检查服务状态...")
    try:
        import requests
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("服务已启动，开始测试")
            asyncio.run(main())
        else:
            print(f"服务状态异常: {response.status_code}")
    except requests.RequestException:
        print("服务未启动，请先启动 Orchestrator 服务:")
        print("cd src/orchestrator && python main.py")
        print("或者运行: python -m src.orchestrator.main") 