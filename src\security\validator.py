"""
安全验证器
提供配置验证、输入验证和安全检查功能
"""

import re
from typing import List, Dict, Any, Optional
from uuid import UUID
from pydantic import BaseModel, Field, validator

from src.common.logger import get_logger
from src.common.config import Settings

logger = get_logger(__name__)


class SecurityValidationError(Exception):
    """安全验证错误"""
    pass


class InputValidator:
    """输入验证器"""
    
    # 常见的危险模式
    DANGEROUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',  # XSS
        r'javascript:',  # JavaScript URL
        r'on\w+\s*=',  # 事件处理器
        r'union\s+select',  # SQL注入
        r'drop\s+table',  # SQL注入
        r'exec\s*\(',  # 代码执行
        r'eval\s*\(',  # 代码执行
        r'__import__',  # Python导入
        r'subprocess',  # 系统命令
    ]
    
    @classmethod
    def validate_text_input(cls, text: str, max_length: int = 10000) -> bool:
        """验证文本输入"""
        if not text or len(text) > max_length:
            return False
        
        # 检查危险模式
        for pattern in cls.DANGEROUS_PATTERNS:
            if re.search(pattern, text, re.IGNORECASE):
                logger.warning(f"Dangerous pattern detected: {pattern}")
                return False
        
        return True
    
    @classmethod
    def validate_uuid(cls, uuid_str: str) -> bool:
        """验证UUID格式"""
        try:
            UUID(uuid_str)
            return True
        except (ValueError, TypeError):
            return False
    
    @classmethod
    def validate_email(cls, email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @classmethod
    def validate_filename(cls, filename: str) -> bool:
        """验证文件名安全性"""
        # 检查路径遍历攻击
        if '..' in filename or '/' in filename or '\\' in filename:
            return False
        
        # 检查危险文件扩展名
        dangerous_extensions = ['.exe', '.bat', '.cmd', '.sh', '.ps1', '.vbs']
        for ext in dangerous_extensions:
            if filename.lower().endswith(ext):
                return False
        
        return True


class ConfigurationValidator:
    """配置验证器"""
    
    @classmethod
    def validate_production_security(cls, settings: Settings) -> List[str]:
        """验证生产环境安全配置"""
        errors = []
        
        if settings.is_production:
            # 检查CORS配置
            if "*" in settings.security.cors_origins:
                errors.append("Production CORS cannot allow all origins (*)")
            
            # 检查密钥强度
            if len(settings.security.secret_key) < 32:
                errors.append("Production secret key must be at least 32 characters")
            
            if settings.security.secret_key == "dev-secret-key-change-in-production":
                errors.append("Default secret key must be changed in production")
            
            # 检查调试模式
            if settings.debug:
                errors.append("Debug mode must be disabled in production")
            
            # 检查API密钥配置
            if not settings.ai.api_key and settings.ai.provider != "mock":
                errors.append("AI API key must be configured in production")
        
        return errors
    
    @classmethod
    def validate_network_security(cls, settings: Settings) -> List[str]:
        """验证网络安全配置"""
        errors = []
        
        # 检查端口配置
        if settings.service.orchestrator_port < 1024 and settings.service.orchestrator_port != 80 and settings.service.orchestrator_port != 443:
            errors.append("Using privileged ports requires special permissions")
        
        # 检查主机绑定
        if settings.service.host == "0.0.0.0" and settings.is_production:
            errors.append("Binding to 0.0.0.0 in production may expose service to external networks")
        
        return errors


class RateLimitValidator:
    """速率限制验证器"""
    
    def __init__(self):
        self._request_counts: Dict[str, List[float]] = {}
        self._max_requests_per_minute = 60
        self._cleanup_interval = 300  # 5分钟清理一次
        self._last_cleanup = 0
    
    def is_rate_limited(self, client_id: str, max_requests: Optional[int] = None) -> bool:
        """检查是否触发速率限制"""
        import time
        
        current_time = time.time()
        max_requests = max_requests or self._max_requests_per_minute
        
        # 定期清理过期记录
        if current_time - self._last_cleanup > self._cleanup_interval:
            self._cleanup_old_requests(current_time)
            self._last_cleanup = current_time
        
        # 获取客户端请求记录
        if client_id not in self._request_counts:
            self._request_counts[client_id] = []
        
        requests = self._request_counts[client_id]
        
        # 移除超过1分钟的请求记录
        cutoff_time = current_time - 60
        requests[:] = [req_time for req_time in requests if req_time > cutoff_time]
        
        # 检查是否超过限制
        if len(requests) >= max_requests:
            logger.warning(f"Rate limit exceeded for client {client_id}: {len(requests)} requests in last minute")
            return True
        
        # 记录当前请求
        requests.append(current_time)
        return False
    
    def _cleanup_old_requests(self, current_time: float) -> None:
        """清理过期的请求记录"""
        cutoff_time = current_time - 300  # 5分钟前
        
        for client_id in list(self._request_counts.keys()):
            requests = self._request_counts[client_id]
            requests[:] = [req_time for req_time in requests if req_time > cutoff_time]
            
            # 如果没有最近的请求，删除客户端记录
            if not requests:
                del self._request_counts[client_id]


class SecurityAuditor:
    """安全审计器"""
    
    @classmethod
    def audit_request(cls, request_data: Dict[str, Any], client_ip: str) -> Dict[str, Any]:
        """审计请求"""
        audit_log = {
            "timestamp": logger._get_timestamp(),
            "client_ip": client_ip,
            "request_size": len(str(request_data)),
            "suspicious_patterns": [],
            "risk_level": "low"
        }
        
        # 检查请求大小
        if audit_log["request_size"] > 100000:  # 100KB
            audit_log["suspicious_patterns"].append("large_request")
            audit_log["risk_level"] = "medium"
        
        # 检查敏感字段
        sensitive_fields = ["password", "secret", "key", "token"]
        for field in sensitive_fields:
            if any(field in str(key).lower() for key in request_data.keys()):
                audit_log["suspicious_patterns"].append(f"sensitive_field_{field}")
        
        # 检查输入内容
        for key, value in request_data.items():
            if isinstance(value, str):
                if not InputValidator.validate_text_input(value):
                    audit_log["suspicious_patterns"].append(f"dangerous_input_{key}")
                    audit_log["risk_level"] = "high"
        
        return audit_log


# 全局实例
input_validator = InputValidator()
config_validator = ConfigurationValidator()
rate_limiter = RateLimitValidator()
security_auditor = SecurityAuditor()
