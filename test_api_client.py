#!/usr/bin/env python3
"""
API客户端测试脚本
测试Orchestrator服务的所有API端点
"""

import asyncio
import httpx
import json
from typing import Dict, Any


class TestOrchestratorClient:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = None
        
    async def __aenter__(self):
        self.client = httpx.AsyncClient(base_url=self.base_url)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()
    
    async def test_health_check(self) -> Dict[str, Any]:
        """测试健康检查端点"""
        response = await self.client.get("/health")
        return {"status": response.status_code, "data": response.json()}
    
    async def test_create_session(self) -> Dict[str, Any]:
        """测试创建会话"""
        payload = {
            "project_id": "test-project-001",
            "user_id": "test-user",
            "language": "python",
            "framework": "pytest"
        }
        response = await self.client.post("/api/v1/sessions", json=payload)
        return {"status": response.status_code, "data": response.json()}
    
    async def test_get_session(self, session_id: str) -> Dict[str, Any]:
        """测试获取会话信息"""
        response = await self.client.get(f"/api/v1/sessions/{session_id}")
        return {"status": response.status_code, "data": response.json()}
    
    async def test_generate_test_cases(self, session_id: str) -> Dict[str, Any]:
        """测试生成测试用例"""
        payload = {
            "session_id": session_id,
            "requirements": "用户登录功能测试，包括正常登录、密码错误、用户名不存在等场景",
            "api_specs": {
                "login": {
                    "method": "POST",
                    "url": "/api/login",
                    "request_schema": {
                        "username": "string",
                        "password": "string"
                    }
                }
            },
            "crypto_requirements": {
                "encrypt_password": {
                    "algorithm": "AES_GCM",
                    "key_id": "password-key"
                }
            }
        }
        response = await self.client.post("/api/v1/test-cases/generate", json=payload)
        return {"status": response.status_code, "data": response.json()}
    
    async def test_generate_script(self, test_cases: list) -> Dict[str, Any]:
        """测试生成测试脚本"""
        payload = {
            "test_cases": test_cases,
            "language": "python",
            "framework": "pytest",
            "crypto_config": {
                "base_url": "http://crypto-service:8080",
                "timeout": 30
            }
        }
        response = await self.client.post("/api/v1/scripts/generate", json=payload)
        return {"status": response.status_code, "data": response.json()}
    
    async def test_execute_script(self, script_content: str) -> Dict[str, Any]:
        """测试执行脚本"""
        payload = {
            "script_content": script_content,
            "environment": "test",
            "parallel": False,
            "timeout": 300
        }
        response = await self.client.post("/api/v1/executions", json=payload)
        return {"status": response.status_code, "data": response.json()}
    
    async def test_get_execution_status(self, execution_id: str) -> Dict[str, Any]:
        """测试获取执行状态"""
        response = await self.client.get(f"/api/v1/executions/{execution_id}")
        return {"status": response.status_code, "data": response.json()}


async def run_full_test():
    """运行完整的API测试流程"""
    print("🚀 开始测试Orchestrator API...")
    
    async with TestOrchestratorClient() as client:
        try:
            # 1. 健康检查
            print("\n1. 测试健康检查...")
            health_result = await client.test_health_check()
            print(f"   状态码: {health_result['status']}")
            print(f"   响应: {json.dumps(health_result['data'], indent=2, ensure_ascii=False)}")
            
            if health_result['status'] != 200:
                print("❌ 健康检查失败，停止测试")
                return
            
            # 2. 创建会话
            print("\n2. 测试创建会话...")
            session_result = await client.test_create_session()
            print(f"   状态码: {session_result['status']}")
            print(f"   响应: {json.dumps(session_result['data'], indent=2, ensure_ascii=False)}")
            
            if session_result['status'] != 200:
                print("❌ 创建会话失败，停止测试")
                return
                
            session_id = session_result['data']['session_id']
            print(f"   ✅ 会话创建成功，ID: {session_id}")
            
            # 3. 获取会话信息
            print("\n3. 测试获取会话信息...")
            get_session_result = await client.test_get_session(session_id)
            print(f"   状态码: {get_session_result['status']}")
            print(f"   响应: {json.dumps(get_session_result['data'], indent=2, ensure_ascii=False)}")
            
            # 4. 生成测试用例
            print("\n4. 测试生成测试用例...")
            test_cases_result = await client.test_generate_test_cases(session_id)
            print(f"   状态码: {test_cases_result['status']}")
            print(f"   响应: {json.dumps(test_cases_result['data'], indent=2, ensure_ascii=False)}")
            
            if test_cases_result['status'] != 200:
                print("❌ 生成测试用例失败")
                return
                
            test_cases = test_cases_result['data']['test_cases']
            print(f"   ✅ 生成了 {len(test_cases)} 个测试用例")
            
            # 5. 生成测试脚本
            print("\n5. 测试生成测试脚本...")
            script_result = await client.test_generate_script(test_cases)
            print(f"   状态码: {script_result['status']}")
            print(f"   响应大小: {len(str(script_result['data']))} 字符")
            
            if script_result['status'] != 200:
                print("❌ 生成测试脚本失败")
                return
                
            script_content = script_result['data']['script_content']
            print(f"   ✅ 脚本生成成功，长度: {len(script_content)} 字符")
            
            # 6. 执行脚本
            print("\n6. 测试执行脚本...")
            execution_result = await client.test_execute_script(script_content)
            print(f"   状态码: {execution_result['status']}")
            print(f"   响应: {json.dumps(execution_result['data'], indent=2, ensure_ascii=False)}")
            
            if execution_result['status'] != 200:
                print("❌ 执行脚本失败")
                return
                
            execution_id = execution_result['data']['execution_id']
            print(f"   ✅ 脚本提交执行成功，ID: {execution_id}")
            
            # 7. 获取执行状态
            print("\n7. 测试获取执行状态...")
            status_result = await client.test_get_execution_status(execution_id)
            print(f"   状态码: {status_result['status']}")
            print(f"   响应: {json.dumps(status_result['data'], indent=2, ensure_ascii=False)}")
            
            print("\n🎉 所有API测试完成！")
            
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(run_full_test()) 