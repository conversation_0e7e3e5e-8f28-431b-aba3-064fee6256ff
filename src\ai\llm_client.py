"""
大语言模型客户端

支持多种LLM Provider：OpenAI、Azure OpenAI、本地模型等
"""

import asyncio
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import json

from langchain_core.language_models import BaseLLM
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage, AIMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_openai import ChatOpenAI, AzureChatOpenAI
from langchain_community.llms import Ollama

from src.common.config import settings
from src.common.logger import get_logger

logger = get_logger(__name__)


class LLMProvider(str, Enum):
    """LLM服务提供商枚举"""
    OPENAI = "openai"
    AZURE_OPENAI = "azure_openai"
    OLLAMA = "ollama"
    MOCK = "mock"  # 用于测试


class LLMClient:
    """
    大语言模型客户端
    
    统一管理不同LLM服务的调用接口
    """
    
    def __init__(self):
        self.provider: Optional[LLMProvider] = None
        self.llm: Optional[BaseLLM] = None
        self.model_name: str = ""
        self.max_tokens: int = 4000
        self.temperature: float = 0.7
        
    async def initialize(
        self,
        provider: LLMProvider = LLMProvider.MOCK,
        model_name: str = "gpt-3.5-turbo",
        **kwargs
    ) -> None:
        """
        初始化LLM客户端
        
        Args:
            provider: LLM服务提供商
            model_name: 模型名称
            **kwargs: 其他配置参数
        """
        try:
            self.provider = provider
            self.model_name = model_name
            self.max_tokens = kwargs.get('max_tokens', 4000)
            self.temperature = kwargs.get('temperature', 0.7)
            
            logger.info(f"Initializing LLM client with provider={provider}, model={model_name}")
            
            if provider == LLMProvider.OPENAI:
                self.llm = await self._init_openai(**kwargs)
            elif provider == LLMProvider.AZURE_OPENAI:
                self.llm = await self._init_azure_openai(**kwargs)
            elif provider == LLMProvider.OLLAMA:
                self.llm = await self._init_ollama(**kwargs)
            elif provider == LLMProvider.MOCK:
                self.llm = await self._init_mock(**kwargs)
            else:
                raise ValueError(f"Unsupported LLM provider: {provider}")
                
            logger.info("LLM client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize LLM client: {e}")
            raise
    
    async def _init_openai(self, **kwargs) -> ChatOpenAI:
        """初始化OpenAI客户端"""
        api_key = kwargs.get('api_key', 'sk-mock-key-for-testing')  # 默认使用Mock key
        
        return ChatOpenAI(
            api_key=api_key,
            model=self.model_name,
            max_tokens=self.max_tokens,
            temperature=self.temperature,
            request_timeout=kwargs.get('timeout', 60)
        )
    
    async def _init_azure_openai(self, **kwargs) -> AzureChatOpenAI:
        """初始化Azure OpenAI客户端"""
        api_key = kwargs.get('api_key', 'mock-azure-key')
        endpoint = kwargs.get('endpoint', 'https://mock.openai.azure.com')
        
        return AzureChatOpenAI(
            azure_api_key=api_key,
            azure_endpoint=endpoint,
            azure_deployment=self.model_name,
            api_version=kwargs.get('api_version', '2024-02-01'),
            max_tokens=self.max_tokens,
            temperature=self.temperature
        )
    
    async def _init_ollama(self, **kwargs) -> Ollama:
        """初始化Ollama本地模型客户端"""
        base_url = kwargs.get('base_url', 'http://localhost:11434')
        
        return Ollama(
            model=self.model_name,
            base_url=base_url,
            temperature=self.temperature
        )
    
    async def _init_mock(self, **kwargs) -> 'MockLLM':
        """初始化Mock客户端（用于测试）"""
        return MockLLM()
    
    async def chat(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        进行对话
        
        Args:
            messages: 对话消息列表
            system_prompt: 系统提示词
            **kwargs: 其他参数
            
        Returns:
            响应结果
        """
        if not self.llm:
            raise ValueError("LLM client not initialized")
            
        try:
            # 构建消息列表
            langchain_messages = []
            
            if system_prompt:
                langchain_messages.append(SystemMessage(content=system_prompt))
            
            for msg in messages:
                role = msg.get('role', 'user')
                content = msg.get('content', '')
                
                if role == 'user':
                    langchain_messages.append(HumanMessage(content=content))
                elif role == 'assistant':
                    langchain_messages.append(AIMessage(content=content))
                elif role == 'system':
                    langchain_messages.append(SystemMessage(content=content))
            
            # 调用模型
            start_time = asyncio.get_event_loop().time()
            
            if hasattr(self.llm, 'ainvoke'):
                response = await self.llm.ainvoke(langchain_messages)
            else:
                # 同步调用的包装
                response = await asyncio.get_event_loop().run_in_executor(
                    None, self.llm.invoke, langchain_messages
                )
            
            end_time = asyncio.get_event_loop().time()
            
            # 记录调用信息
            duration = f"{end_time - start_time:.2f}s"
            input_length = sum(len(msg.get('content', '')) for msg in messages)
            output_length = len(response.content) if hasattr(response, 'content') else 0
            logger.info(f"LLM call completed: provider={self.provider}, model={self.model_name}, duration={duration}, input_length={input_length}, output_length={output_length}")
            
            return {
                'content': response.content if hasattr(response, 'content') else str(response),
                'model': self.model_name,
                'provider': self.provider,
                'usage': {
                    'duration': end_time - start_time,
                    'input_tokens': sum(len(msg.get('content', '').split()) for msg in messages),
                    'output_tokens': len(response.content.split()) if hasattr(response, 'content') else 0
                }
            }
            
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            raise
    
    async def generate_structured(
        self,
        prompt: str,
        schema: Dict[str, Any],
        examples: Optional[List[Dict]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成结构化输出
        
        Args:
            prompt: 提示词
            schema: 输出结构定义
            examples: 示例数据
            **kwargs: 其他参数
            
        Returns:
            结构化数据
        """
        # 构建结构化提示
        schema_str = json.dumps(schema, indent=2, ensure_ascii=False)
        
        structured_prompt = f"""
请根据以下要求生成结构化数据：

{prompt}

输出格式要求：
{schema_str}

请严格按照上述JSON格式输出，不要包含其他内容。
"""
        
        if examples:
            examples_str = "\n\n示例：\n" + "\n".join(
                json.dumps(ex, indent=2, ensure_ascii=False) for ex in examples
            )
            structured_prompt += examples_str
        
        response = await self.chat([
            {'role': 'user', 'content': structured_prompt}
        ])
        
        # 尝试解析JSON
        try:
            content = response['content'].strip()
            # 移除可能的markdown代码块标记
            if content.startswith('```json'):
                content = content[7:]
            if content.startswith('```'):
                content = content[3:]
            if content.endswith('```'):
                content = content[:-3]
            
            return json.loads(content.strip())
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse structured output: {e}")
            logger.error(f"Raw content: {response['content']}")
            raise ValueError(f"Invalid JSON output from LLM: {e}")
    
    def is_available(self) -> bool:
        """检查LLM是否可用"""
        return self.llm is not None


class MockLLM:
    """Mock LLM用于测试"""
    
    def __init__(self):
        self.responses = {
            'test_case_generation': {
                'test_cases': [
                    {
                        'title': '用户名密码登录-正常场景',
                        'description': '验证使用正确的用户名和密码能够成功登录',
                        'type': 'functional',
                        'priority': 'high',
                        'tags': ['login', 'authentication'],
                        'steps': [
                            '打开登录页面',
                            '输入正确的用户名',
                            '输入正确的密码',
                            '点击登录按钮'
                        ],
                        'expected_result': '登录成功，跳转到主页面',
                        'test_data': {
                            'username': 'test_user',
                            'password': 'Test123!'
                        }
                    },
                    {
                        'title': '用户名密码登录-错误密码',
                        'description': '验证使用错误密码无法登录',
                        'type': 'negative',
                        'priority': 'high',
                        'tags': ['login', 'security'],
                        'steps': [
                            '打开登录页面',
                            '输入正确的用户名',
                            '输入错误的密码',
                            '点击登录按钮'
                        ],
                        'expected_result': '登录失败，显示错误提示',
                        'test_data': {
                            'username': 'test_user',
                            'password': 'wrong_password'
                        }
                    }
                ]
            }
        }
    
    def invoke(self, messages):
        """模拟同步调用"""
        return MockResponse(self._get_mock_response(messages))
    
    async def ainvoke(self, messages):
        """模拟异步调用"""
        await asyncio.sleep(0.1)  # 模拟网络延迟
        return MockResponse(self._get_mock_response(messages))
    
    def _get_mock_response(self, messages):
        """生成Mock响应"""
        # 简单的关键词匹配
        content = ' '.join([msg.content for msg in messages if hasattr(msg, 'content')])
        
        if '测试用例' in content or 'test case' in content.lower():
            return json.dumps(self.responses['test_case_generation'], ensure_ascii=False, indent=2)
        
        return "这是一个模拟的AI响应。在实际使用中，这里会调用真实的大模型。"


class MockResponse:
    """Mock响应对象"""
    
    def __init__(self, content: str):
        self.content = content 