"""
TestGenius AI模块测试套件

测试覆盖：
1. LLM客户端功能测试
2. 提示词管理测试
3. 测试用例AI生成测试
4. 脚本生成AI测试
5. 错误处理和降级机制测试
"""

import pytest
import asyncio
import json
from typing import Dict, List, Any
from unittest.mock import AsyncMock, MagicMock, patch

from src.ai.llm_client import LL<PERSON>lient, LLMProvider, MockLLM
from src.ai.prompt_manager import PromptManager, PromptType
from src.ai.test_case_ai import TestCaseAI
from src.ai.script_gen_ai import ScriptGenAI
from src.test_case.models import GenerationContext, GenerationOptions, TestCaseType
from src.orchestrator.models import TestCaseDefinition


class TestLLMClient:
    """LLM客户端测试"""
    
    @pytest.fixture
    async def llm_client(self):
        """创建LLM客户端"""
        client = LLMClient()
        await client.initialize(provider=LLMProvider.MOCK)
        return client
    
    @pytest.mark.asyncio
    async def test_initialization(self, llm_client):
        """测试LLM客户端初始化"""
        assert llm_client.provider == LLMProvider.MOCK
        assert llm_client.llm is not None
        assert llm_client.is_available() is True
    
    @pytest.mark.asyncio
    async def test_basic_chat(self, llm_client):
        """测试基本对话功能"""
        messages = [
            {"role": "user", "content": "生成一个简单的测试用例"}
        ]
        
        response = await llm_client.chat(messages)
        
        assert isinstance(response, dict)
        assert "content" in response
        assert "model" in response
        assert "provider" in response
        assert "usage" in response
        assert response["provider"] == LLMProvider.MOCK
    
    @pytest.mark.asyncio
    async def test_chat_with_system_prompt(self, llm_client):
        """测试带系统提示词的对话"""
        system_prompt = "你是一个专业的测试工程师"
        messages = [
            {"role": "user", "content": "帮我生成登录功能的测试用例"}
        ]
        
        response = await llm_client.chat(messages, system_prompt=system_prompt)
        
        assert response["content"] is not None
        assert len(response["content"]) > 0
    
    @pytest.mark.asyncio
    async def test_structured_generation(self, llm_client):
        """测试结构化输出生成"""
        prompt = "生成用户登录的测试用例"
        schema = {
            "type": "object",
            "properties": {
                "test_cases": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "description": {"type": "string"},
                            "steps": {"type": "array", "items": {"type": "string"}},
                            "expected_result": {"type": "string"}
                        }
                    }
                }
            }
        }
        
        result = await llm_client.generate_structured(prompt, schema)
        
        assert isinstance(result, dict)
        assert "test_cases" in result
        assert isinstance(result["test_cases"], list)
        assert len(result["test_cases"]) > 0
    
    @pytest.mark.asyncio
    async def test_invalid_provider(self):
        """测试无效的LLM提供商"""
        client = LLMClient()
        
        with pytest.raises(ValueError, match="Unsupported LLM provider"):
            await client.initialize(provider="invalid_provider")
    
    @pytest.mark.asyncio
    async def test_uninitialized_client(self):
        """测试未初始化的客户端"""
        client = LLMClient()
        
        with pytest.raises(ValueError, match="LLM client not initialized"):
            await client.chat([{"role": "user", "content": "test"}])


class TestPromptManager:
    """提示词管理器测试"""
    
    @pytest.fixture
    def prompt_manager(self):
        """创建提示词管理器"""
        return PromptManager()
    
    def test_initialization(self, prompt_manager):
        """测试提示词管理器初始化"""
        assert prompt_manager is not None
        templates = prompt_manager.list_templates()
        assert isinstance(templates, list)
        assert len(templates) > 0
    
    def test_get_template(self, prompt_manager):
        """测试获取模板"""
        template = prompt_manager.get_template(PromptType.TEST_CASE_GENERATION)
        
        assert template is not None
        assert isinstance(template, str)
        assert len(template) > 0
        assert "测试用例" in template or "test case" in template.lower()
    
    def test_get_nonexistent_template(self, prompt_manager):
        """测试获取不存在的模板"""
        template = prompt_manager.get_template("nonexistent_template")
        
        assert template is None
    
    def test_render_template(self, prompt_manager):
        """测试模板渲染"""
        template = prompt_manager.get_template(PromptType.TEST_CASE_GENERATION)
        
        variables = {
            "requirements": "用户登录功能",
            "test_types": ["functional", "security"],
            "count": 3
        }
        
        rendered = prompt_manager.render_template(template, variables)
        
        assert isinstance(rendered, str)
        assert "用户登录功能" in rendered
        assert "functional" in rendered
        assert "security" in rendered
    
    def test_validate_template(self, prompt_manager):
        """测试模板验证"""
        valid_template = "请为 {requirements} 生成 {count} 个测试用例"
        invalid_template = "请为 {requirements} 生成 {missing_var} 个测试用例"
        
        variables = {"requirements": "登录功能", "count": 3}
        
        # 有效模板应该通过验证
        is_valid, missing = prompt_manager.validate_template(valid_template, variables)
        assert is_valid is True
        assert len(missing) == 0
        
        # 无效模板应该失败
        is_valid, missing = prompt_manager.validate_template(invalid_template, variables)
        assert is_valid is False
        assert "missing_var" in missing


class TestTestCaseAI:
    """测试用例AI生成器测试"""
    
    @pytest.fixture
    async def test_case_ai(self):
        """创建测试用例AI生成器"""
        ai = TestCaseAI()
        await ai.initialize(provider=LLMProvider.MOCK)
        return ai
    
    @pytest.mark.asyncio
    async def test_initialization(self, test_case_ai):
        """测试初始化"""
        assert test_case_ai.is_initialized is True
        assert test_case_ai.llm_client is not None
        assert test_case_ai.prompt_manager is not None
    
    @pytest.mark.asyncio
    async def test_generate_test_cases_basic(self, test_case_ai):
        """测试基本测试用例生成"""
        context = GenerationContext(
            requirements="用户登录功能测试",
            domain="web应用",
            system_context="电商网站"
        )
        
        options = GenerationOptions(
            test_types=[TestCaseType.FUNCTIONAL],
            count=2,
            priority="high"
        )
        
        result = await test_case_ai.generate_test_cases(context, options)
        
        assert result is not None
        assert isinstance(result, dict)
        assert "test_cases" in result
        assert len(result["test_cases"]) > 0
        
        # 验证生成的测试用例结构
        for test_case in result["test_cases"]:
            assert "title" in test_case
            assert "description" in test_case
            assert "steps" in test_case
            assert "expected_result" in test_case
    
    @pytest.mark.asyncio
    async def test_generate_with_crypto_requirements(self, test_case_ai):
        """测试生成包含加密需求的测试用例"""
        context = GenerationContext(
            requirements="支付接口测试，需要RSA签名验证",
            domain="金融支付",
            system_context="支付网关"
        )
        
        options = GenerationOptions(
            test_types=[TestCaseType.SECURITY],
            count=1,
            priority="high",
            crypto_enabled=True
        )
        
        result = await test_case_ai.generate_test_cases(context, options)
        
        assert result is not None
        assert "test_cases" in result
        
        # 检查是否包含加密相关内容
        test_case_content = json.dumps(result["test_cases"], ensure_ascii=False)
        assert any(keyword in test_case_content.lower() for keyword in ["签名", "加密", "rsa", "signature"])
    
    @pytest.mark.asyncio
    async def test_generate_with_empty_requirements(self, test_case_ai):
        """测试空需求的处理"""
        context = GenerationContext(
            requirements="",
            domain="通用",
            system_context=""
        )
        
        options = GenerationOptions(
            test_types=[TestCaseType.FUNCTIONAL],
            count=1,
            priority="medium"
        )
        
        result = await test_case_ai.generate_test_cases(context, options)
        
        # 应该能处理空需求，生成通用测试用例
        assert result is not None
        assert "test_cases" in result
    
    @pytest.mark.asyncio
    async def test_llm_failure_handling(self):
        """测试LLM故障处理"""
        ai = TestCaseAI()
        
        # 不初始化LLM客户端，模拟故障
        context = GenerationContext(
            requirements="测试需求",
            domain="测试",
            system_context="测试"
        )
        
        options = GenerationOptions(
            test_types=[TestCaseType.FUNCTIONAL],
            count=1,
            priority="medium"
        )
        
        # 应该使用降级机制
        result = await ai.generate_test_cases(context, options)
        
        assert result is not None
        assert "test_cases" in result
        # 降级生成的测试用例应该是模板化的
        assert len(result["test_cases"]) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
