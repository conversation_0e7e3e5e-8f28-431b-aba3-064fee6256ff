#!/usr/bin/env python3
"""
启动TestGenius Orchestrator服务的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.orchestrator.main import main

if __name__ == "__main__":
    print("启动TestGenius Orchestrator服务...")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"服务启动失败: {e}")
        import traceback
        traceback.print_exc() 