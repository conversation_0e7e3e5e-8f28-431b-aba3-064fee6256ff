"""
提示词管理器

管理各种AI任务的提示词模板
"""

from typing import Dict, List, Optional, Any
from enum import Enum
import json

from src.common.logger import get_logger

logger = get_logger(__name__)


class PromptType(str, Enum):
    """提示词类型枚举"""
    TEST_CASE_GENERATION = "test_case_generation"
    REQUIREMENT_ANALYSIS = "requirement_analysis"
    SCRIPT_GENERATION = "script_generation"
    ERROR_ANALYSIS = "error_analysis"
    CODE_REVIEW = "code_review"


class PromptManager:
    """
    提示词管理器
    
    统一管理各种AI任务的提示词模板
    """
    
    def __init__(self):
        self.prompts: Dict[str, Dict[str, str]] = {}
        self._load_default_prompts()
    
    def _load_default_prompts(self):
        """加载默认提示词模板"""
        self.prompts = {
            PromptType.TEST_CASE_GENERATION: {
                "system": """你是一个专业的测试工程师，专门负责根据需求生成高质量的测试用例。

你的任务是：
1. 分析用户需求，理解功能点和业务逻辑
2. 设计全面的测试场景，包括正常流程、边界条件、异常情况
3. 生成结构化的测试用例，包含清晰的测试步骤和预期结果
4. 考虑安全性测试，特别是涉及加密、签名等安全功能
5. 评估测试优先级和风险等级

输出要求：
- 测试用例要全面覆盖各种场景
- 测试步骤要具体、可执行
- 预期结果要明确、可验证
- 包含适当的测试数据
- 标注测试类型和优先级""",
                
                "user": """请根据以下需求生成测试用例：

需求描述：
{requirement_text}

上下文信息：
- 领域：{domain}
- 系统类型：{system_type}
- 是否需要加密支持：{crypto_enabled}

生成选项：
- 最大用例数量：{max_test_cases}
- 包含安全测试：{include_security_tests}

请生成JSON格式的测试用例，包含以下字段：
- title: 测试用例标题
- description: 详细描述
- type: 测试类型 (functional/boundary/negative/security/performance)
- priority: 优先级 (high/medium/low)
- tags: 标签列表
- steps: 测试步骤列表
- expected_result: 预期结果
- test_data: 测试数据
- crypto_requirements: 加密需求 (如果适用)"""
            },
            
            PromptType.REQUIREMENT_ANALYSIS: {
                "system": """你是一个业务分析专家，专门负责分析和理解软件需求。

你的任务是：
1. 解析需求文本，提取关键信息
2. 识别功能点和非功能性需求
3. 分析业务流程和用户场景
4. 识别潜在的风险点和复杂度
5. 提供结构化的需求分析报告

输出要求：
- 功能点要清晰、完整
- 业务流程要逻辑清楚
- 风险识别要准确
- 建议要具有可操作性""",
                
                "user": """请分析以下需求：

{requirement_text}

请从以下维度进行分析：
1. 核心功能点
2. 业务流程
3. 用户角色和权限
4. 数据流转
5. 安全要求
6. 性能要求
7. 潜在风险点
8. 测试重点"""
            },
            
            PromptType.SCRIPT_GENERATION: {
                "system": """你是一个资深的测试自动化工程师，专门负责生成高质量的测试脚本。

你的任务是：
1. 根据测试用例生成可执行的测试脚本
2. 选择合适的测试框架和工具
3. 处理数据驱动和参数化测试
4. 集成加密、签名等安全功能
5. 添加适当的日志和断言
6. 确保脚本的可维护性和可读性

支持的语言和框架：
- Python: pytest, requests, selenium
- Java: TestNG, JUnit, RestAssured
- JavaScript: Jest, Mocha, Playwright

输出要求：
- 代码要规范、可读
- 注释要详细、清楚
- 错误处理要完善
- 支持并发执行""",
                
                "user": """请根据以下测试用例生成{language}测试脚本：

测试用例：
{test_case}

配置信息：
- 目标语言：{language}
- 测试框架：{framework}
- 是否包含加密：{crypto_enabled}
- 执行环境：{environment}

请生成完整的测试脚本，包括：
1. 导入和配置
2. 测试数据准备
3. 测试步骤实现
4. 断言验证
5. 清理和资源释放"""
            },
            
            PromptType.ERROR_ANALYSIS: {
                "system": """你是一个测试分析专家，专门负责分析测试执行中的错误和失败。

你的任务是：
1. 分析错误日志和堆栈信息
2. 识别根本原因
3. 提供解决方案建议
4. 评估影响范围
5. 生成改进建议

分析维度：
- 错误类型分类
- 根因分析
- 影响评估
- 解决方案
- 预防措施""",
                
                "user": """请分析以下测试执行错误：

错误信息：
{error_message}

执行日志：
{execution_log}

测试环境：
{environment_info}

请提供：
1. 错误分类和严重程度
2. 可能的根本原因
3. 具体的解决方案
4. 预防类似问题的建议"""
            }
        }
        
        logger.info(f"Loaded {len(self.prompts)} prompt templates")
    
    def get_prompt(
        self,
        prompt_type: PromptType,
        variables: Dict[str, Any],
        role: str = "user"
    ) -> str:
        """
        获取填充变量后的提示词
        
        Args:
            prompt_type: 提示词类型
            variables: 变量字典
            role: 角色 (system/user)
            
        Returns:
            填充后的提示词
        """
        if prompt_type not in self.prompts:
            raise ValueError(f"Unknown prompt type: {prompt_type}")
        
        template = self.prompts[prompt_type].get(role, "")
        if not template:
            raise ValueError(f"No {role} prompt found for type: {prompt_type}")
        
        try:
            return template.format(**variables)
        except KeyError as e:
            logger.error(f"Missing variable in prompt template: {e}")
            raise ValueError(f"Missing required variable: {e}")
    
    def get_system_prompt(self, prompt_type: PromptType) -> str:
        """获取系统提示词"""
        return self.get_prompt(prompt_type, {}, "system")
    
    def add_custom_prompt(
        self,
        prompt_type: str,
        system_prompt: str,
        user_prompt: str
    ) -> None:
        """
        添加自定义提示词模板
        
        Args:
            prompt_type: 提示词类型
            system_prompt: 系统提示词
            user_prompt: 用户提示词
        """
        self.prompts[prompt_type] = {
            "system": system_prompt,
            "user": user_prompt
        }
        logger.info(f"Added custom prompt type: {prompt_type}")
    
    def validate_prompt(self, prompt_type: PromptType, variables: Dict[str, Any]) -> bool:
        """
        验证提示词模板的变量是否完整
        
        Args:
            prompt_type: 提示词类型
            variables: 变量字典
            
        Returns:
            是否验证通过
        """
        try:
            self.get_prompt(prompt_type, variables, "user")
            return True
        except (ValueError, KeyError):
            return False
    
    def get_required_variables(self, prompt_type: PromptType) -> List[str]:
        """
        获取提示词模板所需的变量列表
        
        Args:
            prompt_type: 提示词类型
            
        Returns:
            变量名称列表
        """
        if prompt_type not in self.prompts:
            return []
        
        import re
        
        template = self.prompts[prompt_type].get("user", "")
        # 提取形如 {variable} 的变量
        variables = re.findall(r'\{(\w+)\}', template)
        return list(set(variables))
    
    def list_prompt_types(self) -> List[str]:
        """获取所有可用的提示词类型"""
        return list(self.prompts.keys())
    
    def list_templates(self) -> List[str]:
        """列出所有可用的模板（别名方法）"""
        return self.list_prompt_types() 