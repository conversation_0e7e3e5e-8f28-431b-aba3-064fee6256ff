[project]
name = "testgenius"
version = "0.1.0"
description = "智能化测试用例生成与执行平台，集成加密签名能力的AI驱动测试自动化解决方案"
authors = [
    {name = "TestGenius Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
keywords = ["ai", "testing", "automation", "crypto", "agent"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # Web框架
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    # 异步支持
    "asyncio-mqtt>=0.16.0",
    "aiohttp>=3.9.0",
    "aiofiles>=23.2.0",
    # AI框架
    "langchain>=0.1.0",
    "langchain-community>=0.0.10",
    "langchain-openai>=0.0.5",
    "langgraph>=0.0.20",
    # 数据处理
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "sqlalchemy[asyncio]>=2.0.0",
    "alembic>=1.13.0",
    # 数据库驱动
    "asyncpg>=0.29.0", # PostgreSQL
    "redis[hiredis]>=5.0.0", # Redis
    # 向量数据库
    "chromadb>=0.4.0",
    "sentence-transformers>=2.2.0",
    # 加密相关
    "cryptography>=41.0.0",
    "pycryptodome>=3.19.0",
    # 配置管理
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0.1",
    "toml>=0.10.2",
    # 日志和监控
    "structlog>=23.2.0",
    "prometheus-client>=0.19.0",
    # 工具库
    "click>=8.1.0",
    "rich>=13.7.0",
    "jinja2>=3.1.0",
    "httpx>=0.25.0",
    "tenacity>=8.2.0",
    "requests>=2.32.4",
    "langchain-core>=0.3.66",
    "hvac>=2.3.0", # Vault客户端
]

[project.optional-dependencies]
dev = [
    # 开发工具
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
    
    # 测试
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",  # 用于测试API
    
    # 开发服务器
    "watchdog>=3.0.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
]

crypto = [
    # Rust扩展依赖 (后续添加)
    "maturin>=1.4.0",  # 用于构建Rust扩展
]

deploy = [
    "gunicorn>=21.2.0",
    "docker>=6.1.0",
    "kubernetes>=28.1.0",
]

[project.scripts]
testgenius = "src.orchestrator.cli:main"
tg-crypto = "src.crypto.cli:main"
tg-scheduler = "src.scheduler.cli:main"

[project.urls]
Homepage = "https://github.com/testgenius/testgenius"
Documentation = "https://docs.testgenius.dev"
Repository = "https://github.com/testgenius/testgenius"
"Bug Tracker" = "https://github.com/testgenius/testgenius/issues"

[build-system]
requires = ["hatchling", "maturin>=1.4.0"]
build-backend = "hatchling.build"

[tool.hatch.build]
include = [
    "src/**/*.py",
    "templates/**/*",
    "docs/**/*.md",
]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "langchain.*",
    "langgraph.*",
    "chromadb.*",
    "sentence_transformers.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
pythonpath = ["src"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "crypto: marks tests as crypto-related tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
] 
