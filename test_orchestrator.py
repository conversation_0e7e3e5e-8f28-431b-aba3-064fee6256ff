#!/usr/bin/env python3
"""
测试Orchestrator模块导入
"""

def test_imports():
    """测试各个模块的导入"""
    try:
        print("Testing imports...")
        
        # 测试基础模块
        print("1. Importing common modules...")
        from src.common.config import settings
        print("   ✓ Config imported")
        
        from src.common.logger import get_logger
        print("   ✓ Logger imported")
        
        # 测试数据模型
        print("2. Importing orchestrator models...")
        try:
            from src.orchestrator.models import SessionContext
            print("   ✓ SessionContext imported")
        except Exception as e:
            print(f"   ✗ SessionContext import failed: {e}")
            return False
        
        try:
            from src.orchestrator.models import TestCaseGenerationRequest
            print("   ✓ TestCaseGenerationRequest imported")
        except Exception as e:
            print(f"   ✗ TestCaseGenerationRequest import failed: {e}")
            return False
        
        # 测试服务类
        print("3. Importing orchestrator service...")
        try:
            from src.orchestrator.service import OrchestratorService
            print("   ✓ OrchestratorService imported")
        except Exception as e:
            print(f"   ✗ OrchestratorService import failed: {e}")
            return False
        
        # 测试API模块
        print("4. Importing orchestrator API...")
        try:
            from src.orchestrator.api import router
            print("   ✓ API router imported")
        except Exception as e:
            print(f"   ✗ API router import failed: {e}")
            return False
        
        print("✓ All imports successful!")
        return True
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_service_creation():
    """测试服务实例创建"""
    try:
        print("\nTesting service creation...")
        from src.orchestrator.service import OrchestratorService
        
        service = OrchestratorService()
        print("✓ OrchestratorService instance created")
        
        print(f"Service ready: {service.is_ready()}")
        return True
        
    except Exception as e:
        print(f"✗ Service creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("TestGenius Orchestrator Module Test")
    print("=" * 40)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试服务创建
        service_success = test_service_creation()
        
        if service_success:
            print("\n✓ All tests passed!")
        else:
            print("\n✗ Service creation test failed!")
    else:
        print("\n✗ Import test failed!") 