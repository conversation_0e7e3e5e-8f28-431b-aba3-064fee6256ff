{"modules": {"src.common.config": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\common\\config.py", "classes": ["AISettings", "BaseSettings", "DatabaseSettings", "Field", "List", "MonitoringSettings", "Optional", "SecuritySettings", "ServiceSettings", "Settings"]}, "src.common.logger": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\common\\logger.py", "classes": []}, "src.orchestrator.service": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\orchestrator\\service.py", "classes": ["Any", "AsyncGenerator", "Dict", "ExecutionRequest", "ExecutionResponse", "ExecutionState", "ExecutionStatus", "<PERSON><PERSON><PERSON><PERSON>", "List", "Optional", "OrchestratorService", "ScriptDefinition", "ScriptGenerationRequest", "ScriptGenerationResponse", "SessionContext", "TaskStatus", "TestCaseDefinition", "TestCaseGenerationRequest", "TestCaseGenerationResponse", "TestCaseGenerator", "UUID"]}, "src.orchestrator.models": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\orchestrator\\models.py", "classes": ["Any", "BaseModel", "CryptoAlgorithm", "CryptoRequirement", "Dict", "Enum", "ExecutionRequest", "ExecutionResponse", "ExecutionResult", "ExecutionState", "ExecutionStatus", "FeedbackData", "FeedbackType", "Field", "HashAlgorithm", "List", "Optional", "ScriptDefinition", "ScriptGenerationRequest", "ScriptGenerationResponse", "SessionContext", "SignatureRequirement", "TaskStatus", "TestCaseDefinition", "TestCaseGenerationRequest", "TestCaseGenerationResponse", "UUID"]}, "src.orchestrator.api": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\orchestrator\\api.py", "classes": ["APIRouter", "Depends", "Dict", "ExecutionRequest", "ExecutionResponse", "ExecutionStatus", "FeedbackData", "HTTPException", "OrchestratorService", "ScriptGenerationRequest", "ScriptGenerationResponse", "SessionContext", "StreamingResponse", "TestCaseGenerationRequest", "TestCaseGenerationResponse", "UUID"]}, "src.ai.llm_client": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\ai\\llm_client.py", "classes": ["AIMessage", "Any", "AzureChatOpenAI", "BaseLLM", "BaseMessage", "ChatOpenAI", "ChatPromptTemplate", "Dict", "Enum", "HumanMessage", "LLMClient", "<PERSON><PERSON><PERSON><PERSON>", "List", "MockLLM", "MockResponse", "Ollama", "Optional", "SystemMessage", "Union"]}, "src.ai.prompt_manager": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\ai\\prompt_manager.py", "classes": ["Any", "Dict", "Enum", "List", "Optional", "PromptManager", "PromptType"]}, "src.ai.test_case_ai": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\ai\\test_case_ai.py", "classes": ["Any", "Dict", "GenerationContext", "GenerationOptions", "LLMClient", "<PERSON><PERSON><PERSON><PERSON>", "List", "Optional", "PromptManager", "PromptType", "TestCaseAI", "TestCaseDefinition"]}, "src.ai.script_gen_ai": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\ai\\script_gen_ai.py", "classes": ["Any", "Dict", "LLMClient", "<PERSON><PERSON><PERSON><PERSON>", "List", "Optional", "PromptManager", "PromptType", "ScriptGenAI", "TestCaseDefinition"]}, "src.test_case.generator": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\test_case\\generator.py", "classes": ["Any", "CryptoAlgorithm", "CryptoRequirement", "Dict", "GenerationContext", "GenerationOptions", "GenerationResult", "HashAlgorithm", "<PERSON><PERSON><PERSON><PERSON>", "List", "Optional", "RiskAssessment", "RiskLevel", "SignatureRequirement", "TestCaseAI", "TestCaseDefinition", "TestCaseGenerationRequest", "TestCaseGenerator", "TestCaseTemplate", "TestCaseType", "TestPriority", "UUID"]}, "src.test_case.models": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\test_case\\models.py", "classes": ["Any", "BaseModel", "CryptoRequirement", "Dict", "Enum", "Field", "GenerationContext", "GenerationMetadata", "GenerationOptions", "GenerationResult", "List", "Optional", "RiskAssessment", "RiskLevel", "TestCaseDefinition", "TestCaseGenerationResult", "TestCaseTemplate", "TestCaseType", "TestPriority", "UUID"]}, "src.crypto.client": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\crypto\\client.py", "classes": ["Any", "CryptoClient", "CryptoOperation", "CryptoRequest", "CryptoResponse", "CryptoStats", "Dict", "EncryptionAlgorithm", "EncryptionConfig", "HashAlgorithm", "Optional", "SignatureAlgorithm", "SignatureConfig", "UUID", "Union", "VaultConfig"]}, "src.crypto.models": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\crypto\\models.py", "classes": ["Any", "BaseModel", "CryptoOperation", "CryptoRequest", "CryptoResponse", "CryptoStats", "Dict", "EncryptionAlgorithm", "EncryptionConfig", "Enum", "Field", "HashAlgorithm", "KeyConfig", "KeyType", "List", "Optional", "SignatureAlgorithm", "SignatureConfig", "UUID", "Union", "VaultConfig"]}}, "services": {"configuration": {"status": "✅ 正常", "config": {"version": "0.1.0", "environment": "development", "debug": true, "service_port": 8000, "log_level": "INFO", "ai_provider": "mock"}}, "ai": {"status": "✅ 正常", "llm_client": "已初始化", "prompt_templates": 4, "test_case_ai": "已初始化"}, "crypto": {"status": "✅ 正常", "client": "已初始化", "test_result": "⚠️ 加密测试跳过（需要密钥配置）"}, "orchestrator": {"status": "✅ 正常", "initialized": true, "ready": true, "session_created": "c0ea3b14-f6ec-4ad1-9dc9-9e96c65e818c", "test_cases_generated": 1}}, "apis": {"rest_api": {"status": "✅ 正常", "app_created": true, "total_routes": 16, "api_routes": 9, "routes": [{"path": "/api/v1/sessions", "methods": ["POST"]}, {"path": "/api/v1/sessions/{session_id}", "methods": ["GET"]}, {"path": "/api/v1/test-cases/generate", "methods": ["POST"]}, {"path": "/api/v1/scripts/generate", "methods": ["POST"]}, {"path": "/api/v1/executions", "methods": ["POST"]}, {"path": "/api/v1/executions/{execution_id}/status", "methods": ["GET"]}, {"path": "/api/v1/executions/{execution_id}/logs", "methods": ["GET"]}, {"path": "/api/v1/feedback", "methods": ["POST"]}, {"path": "/api/v1/health", "methods": ["GET"]}]}}, "functionality": {"test_case_generation": {"status": "✅ 正常", "generator": "已初始化", "test_cases_generated": 2, "ai_enabled": true}, "missing": {"missing_modules": [], "partial_modules": ["脚本生成模块", "执行调度模块", "结果分析模块", "反馈闭环模块"], "functionality_status": {"实时脚本执行": "❌ 未实现", "执行结果分析": "❌ 未实现", "智能反馈处理": "❌ 未实现", "安全扫描集成": "❌ 未实现", "CI/CD集成": "❌ 未实现", "监控告警": "❌ 未实现", "数据库持久化": "❌ 未实现", "用户认证授权": "❌ 未实现", "多租户支持": "❌ 未实现", "插件系统": "❌ 未实现"}}}, "issues": [], "summary": {"总体状态": "🟡 部分完成", "完成度": "100.0%", "模块状态": "13/13 成功", "服务状态": "4/4 成功", "问题数量": 0, "核心功能": {"配置系统": "✅", "AI模块": "✅", "加密模块": "✅", "测试用例生成": "✅", "编排服务": "✅", "API接口": "✅"}}}