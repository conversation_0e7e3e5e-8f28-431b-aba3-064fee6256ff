#!/usr/bin/env python3
"""
快速功能验证脚本
"""

import requests
import json
import time
import sys

def test_api():
    """快速API测试"""
    base_url = "http://localhost:8000"
    
    print("🚀 TestGenius 快速验证开始")
    print("=" * 50)
    
    # 1. 健康检查
    print("1️⃣ 健康检查...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务运行正常")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接服务: {e}")
        return False
    
    # 2. 直接测试测试用例生成 (绕过会话创建)
    print("\n2️⃣ 测试用例生成...")
    test_data = {
        "requirement_text": "测试用户登录功能，支持手机号和邮箱登录",
        "context": {
            "domain": "Web应用",
            "system_type": "用户系统"
        },
        "generation_options": {
            "max_test_cases": 3
        }
    }
    
    try:
        # 先创建会话
        session_response = requests.post(f"{base_url}/api/v1/sessions", timeout=10)
        if session_response.status_code != 200:
            print(f"❌ 会话创建失败: {session_response.status_code}")
            print(f"   响应: {session_response.text}")
            return False
        
        session_data = session_response.json()
        session_id = session_data.get('session_id')
        print(f"✅ 会话创建成功: {session_id}")
        
        # 添加session_id到测试数据
        test_data['session_id'] = session_id
        
        # 生成测试用例
        response = requests.post(
            f"{base_url}/api/v1/test-cases/generate",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功生成 {len(result.get('test_cases', []))} 个测试用例")
            
            # 显示第一个测试用例
            test_cases = result.get('test_cases', [])
            if test_cases:
                first_case = test_cases[0]
                print(f"   📋 示例用例: {first_case.get('title', 'N/A')}")
                print(f"   🏷️  类型: {first_case.get('type', 'N/A')}")
                print(f"   ⭐ 优先级: {first_case.get('priority', 'N/A')}")
            
            return True
        else:
            print(f"❌ 测试用例生成失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试用例生成异常: {e}")
        return False

def main():
    """主函数"""
    print("TestGenius 快速验证工具")
    print(f"验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if test_api():
        print("\n🎉 所有测试通过！TestGenius 运行正常")
        print("\n📖 接下来可以:")
        print("   - 访问 http://localhost:8000/docs 查看API文档")
        print("   - 运行 test_orchestrator_complete.py 进行完整测试")
        print("   - 开始开发更多功能模块")
        return 0
    else:
        print("\n⚠️  测试失败，请检查:")
        print("   - 服务是否正在运行")
        print("   - 端口8000是否被占用")
        print("   - 查看服务端错误日志")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 