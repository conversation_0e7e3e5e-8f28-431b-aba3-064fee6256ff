"""
Orchestrator 数据模型

定义API请求和响应的数据结构
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ExecutionState(str, Enum):
    """执行状态枚举"""
    QUEUED = "queued"
    PREPARING = "preparing"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class CryptoAlgorithm(str, Enum):
    """加密算法枚举"""
    AES_GCM = "AES-GCM"
    AES_CBC = "AES-CBC"
    DES3 = "3DES"
    RSA2 = "RSA2"
    SM2 = "SM2"
    SM4 = "SM4"


class HashAlgorithm(str, Enum):
    """哈希算法枚举"""
    SHA256 = "SHA256"
    SHA1 = "SHA1"
    MD5 = "MD5"
    SM3 = "SM3"


class SessionContext(BaseModel):
    """会话上下文"""
    session_id: UUID = Field(default_factory=uuid4, description="会话ID")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="会话元数据")
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class CryptoRequirement(BaseModel):
    """加密需求配置"""
    enabled: bool = Field(default=False, description="是否启用加密")
    algorithm: Optional[CryptoAlgorithm] = Field(None, description="加密算法")
    key_id: Optional[str] = Field(None, description="密钥ID")
    fields: List[str] = Field(default_factory=list, description="需要加密的字段")
    
    
class SignatureRequirement(BaseModel):
    """签名需求配置"""
    enabled: bool = Field(default=False, description="是否启用签名")
    algorithm: Optional[str] = Field(None, description="签名算法")
    key_id: Optional[str] = Field(None, description="签名密钥ID")
    hash_algorithm: Optional[HashAlgorithm] = Field(None, description="哈希算法")


class TestCaseDefinition(BaseModel):
    """测试用例定义"""
    test_case_id: UUID = Field(default_factory=uuid4, description="测试用例ID")
    title: str = Field(..., description="测试用例标题")
    description: str = Field(..., description="测试用例描述")
    scenario: str = Field(..., description="测试场景")
    preconditions: List[str] = Field(default_factory=list, description="前置条件")
    test_steps: List[str] = Field(..., description="测试步骤")
    expected_results: List[str] = Field(..., description="期望结果")
    test_data: Dict[str, Any] = Field(default_factory=dict, description="测试数据")
    crypto_requirements: Optional[CryptoRequirement] = Field(None, description="加密需求")
    signature_requirements: Optional[SignatureRequirement] = Field(None, description="签名需求")
    priority: str = Field(default="medium", description="优先级")
    tags: List[str] = Field(default_factory=list, description="标签")
    risk_level: str = Field(default="medium", description="风险等级")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class ScriptDefinition(BaseModel):
    """脚本定义"""
    script_id: UUID = Field(default_factory=uuid4, description="脚本ID")
    test_case_id: UUID = Field(..., description="关联的测试用例ID")
    language: str = Field(..., description="脚本语言")
    framework: str = Field(..., description="测试框架")
    content: str = Field(..., description="脚本内容")
    dependencies: List[str] = Field(default_factory=list, description="依赖列表")
    environment_config: Dict[str, Any] = Field(default_factory=dict, description="环境配置")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class TestCaseGenerationRequest(BaseModel):
    """测试用例生成请求"""
    session_id: UUID = Field(..., description="会话ID")
    requirement_text: str = Field(..., description="需求文本")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="上下文信息")
    generation_options: Optional[Dict[str, Any]] = Field(default_factory=dict, description="生成选项")
    crypto_enabled: bool = Field(default=False, description="是否启用加密功能")
    target_coverage: Optional[float] = Field(None, description="目标覆盖率")
    
    class Config:
        json_encoders = {UUID: str}


class TestCaseGenerationResponse(BaseModel):
    """测试用例生成响应"""
    task_id: UUID = Field(default_factory=uuid4, description="任务ID")
    session_id: UUID = Field(..., description="会话ID")
    status: TaskStatus = Field(default=TaskStatus.COMPLETED, description="任务状态")
    test_cases: List[TestCaseDefinition] = Field(..., description="生成的测试用例")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="生成元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class ScriptGenerationRequest(BaseModel):
    """脚本生成请求"""
    session_id: UUID = Field(..., description="会话ID")
    test_case_ids: List[UUID] = Field(..., description="测试用例ID列表")
    target_language: str = Field(default="python", description="目标语言")
    target_framework: str = Field(default="pytest", description="目标框架")
    generation_options: Optional[Dict[str, Any]] = Field(default_factory=dict, description="生成选项")
    
    class Config:
        json_encoders = {UUID: str}


class ScriptGenerationResponse(BaseModel):
    """脚本生成响应"""
    task_id: UUID = Field(default_factory=uuid4, description="任务ID")
    session_id: UUID = Field(..., description="会话ID")
    status: TaskStatus = Field(default=TaskStatus.COMPLETED, description="任务状态")
    scripts: List[ScriptDefinition] = Field(..., description="生成的脚本")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="生成元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class ExecutionRequest(BaseModel):
    """执行请求"""
    session_id: UUID = Field(..., description="会话ID")
    script_ids: List[UUID] = Field(..., description="脚本ID列表")
    execution_options: Optional[Dict[str, Any]] = Field(default_factory=dict, description="执行选项")
    environment: str = Field(default="test", description="执行环境")
    parallel: bool = Field(default=True, description="是否并行执行")
    timeout: Optional[int] = Field(None, description="超时时间(秒)")
    
    class Config:
        json_encoders = {UUID: str}


class ExecutionResponse(BaseModel):
    """执行响应"""
    execution_id: UUID = Field(default_factory=uuid4, description="执行ID")
    session_id: UUID = Field(..., description="会话ID")
    status: ExecutionState = Field(default=ExecutionState.QUEUED, description="执行状态")
    script_count: int = Field(..., description="脚本数量")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class ExecutionResult(BaseModel):
    """执行结果"""
    script_id: UUID = Field(..., description="脚本ID")
    status: ExecutionState = Field(..., description="执行状态")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    duration: Optional[float] = Field(None, description="执行时长(秒)")
    exit_code: Optional[int] = Field(None, description="退出码")
    stdout: Optional[str] = Field(None, description="标准输出")
    stderr: Optional[str] = Field(None, description="标准错误")
    artifacts: List[str] = Field(default_factory=list, description="产物文件路径")
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class ExecutionStatus(BaseModel):
    """执行状态"""
    execution_id: UUID = Field(..., description="执行ID")
    session_id: UUID = Field(..., description="会话ID")
    status: ExecutionState = Field(..., description="执行状态")
    progress: float = Field(default=0.0, description="执行进度(0-1)")
    total_scripts: int = Field(..., description="总脚本数")
    completed_scripts: int = Field(default=0, description="已完成脚本数")
    failed_scripts: int = Field(default=0, description="失败脚本数")
    results: List[ExecutionResult] = Field(default_factory=list, description="执行结果")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class FeedbackType(str, Enum):
    """反馈类型枚举"""
    TEST_CASE_QUALITY = "test_case_quality"
    SCRIPT_ACCURACY = "script_accuracy"
    EXECUTION_ISSUE = "execution_issue"
    GENERAL_FEEDBACK = "general_feedback"


class FeedbackData(BaseModel):
    """反馈数据模型"""
    feedback_id: UUID = Field(default_factory=uuid4, description="反馈ID")
    session_id: UUID = Field(..., description="会话ID")
    feedback_type: FeedbackType = Field(..., description="反馈类型")
    target_id: Optional[UUID] = Field(None, description="目标对象ID（测试用例或脚本ID）")
    rating: Optional[int] = Field(None, ge=1, le=5, description="评分（1-5）")
    comment: Optional[str] = Field(None, max_length=1000, description="评论")
    suggestions: List[str] = Field(default_factory=list, description="改进建议")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")

    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }