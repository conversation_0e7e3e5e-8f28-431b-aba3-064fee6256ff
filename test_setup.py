#!/usr/bin/env python3
"""
简单的设置验证脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config():
    """测试配置模块"""
    try:
        from src.common.config import settings, get_settings
        print("✓ 配置模块导入成功")
        
        print(f"  - 版本: {settings.version}")
        print(f"  - 环境: {settings.environment}")
        print(f"  - 开发模式: {settings.is_development}")
        
        return True
    except Exception as e:
        print(f"✗ 配置模块测试失败: {e}")
        return False

def test_logger():
    """测试日志模块"""
    try:
        from src.common.logger import get_logger
        logger = get_logger("test")
        logger.info("日志模块测试成功")
        print("✓ 日志模块导入成功")
        return True
    except Exception as e:
        print(f"✗ 日志模块测试失败: {e}")
        return False

def test_project_structure():
    """测试项目结构"""
    required_dirs = [
        "src",
        "src/common",
        "src/orchestrator", 
        "src/crypto",
        "tests",
        "docs"
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✓ {dir_path} 目录存在")
        else:
            print(f"✗ {dir_path} 目录不存在")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    print("TestGenius 项目设置验证")
    print("=" * 40)
    
    tests = [
        ("项目结构", test_project_structure),
        ("配置模块", test_config),
        ("日志模块", test_logger),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n测试 {test_name}:")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"✗ {test_name} 测试出错: {e}")
            results.append(False)
    
    print("\n" + "=" * 40)
    print("测试总结:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ 所有测试通过 ({passed}/{total})")
        print("项目基础设置完成！")
        return 0
    else:
        print(f"✗ 部分测试失败 ({passed}/{total})")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 