"""
Orchestrator 服务主入口

使用 FastAPI 构建异步 Web 服务，提供 RESTful API 和 WebSocket 接口
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from src.common.config import settings
from src.common.logger import get_logger
from src.orchestrator.api import router as api_router, get_orchestrator_service
from src.orchestrator.service import OrchestratorService

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    应用生命周期管理
    
    在应用启动时初始化服务，在关闭时清理资源
    """
    logger.info("Starting TestGenius Orchestrator service")
    
    # 初始化服务
    orchestrator_service = OrchestratorService()
    await orchestrator_service.initialize()
    
    # 将服务实例存储到应用状态中
    app.state.orchestrator_service = orchestrator_service
    
    # 设置依赖注入
    def get_service():
        return orchestrator_service
    
    # 覆盖依赖注入函数
    app.dependency_overrides[get_orchestrator_service] = get_service
    
    logger.info(
        "Orchestrator service started successfully",
        extra={
            "port": settings.service.orchestrator_port,
            "environment": settings.environment
        }
    )
    
    yield
    
    # 清理资源
    logger.info("Shutting down Orchestrator service")
    await orchestrator_service.cleanup()
    logger.info("Orchestrator service stopped")


def create_app() -> FastAPI:
    """
    创建 FastAPI 应用实例
    
    Returns:
        FastAPI 应用实例
    """
    app = FastAPI(
        title="TestGenius Orchestrator",
        description="智能化测试用例生成与执行平台 - 编排服务",
        version=settings.version,
        lifespan=lifespan,
        docs_url="/docs" if settings.is_development else None,
        redoc_url="/redoc" if settings.is_development else None,
    )
    
    # 配置 CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.security.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    app.include_router(api_router, prefix="/api/v1")
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查端点"""
        return {"status": "healthy", "service": "orchestrator"}
    
    @app.get("/health/ready")
    async def readiness_check():
        """就绪检查端点"""
        try:
            orchestrator_service = app.state.orchestrator_service
            if orchestrator_service.is_ready():
                return {"status": "ready", "service": "orchestrator"}
            else:
                raise HTTPException(status_code=503, detail="Service not ready")
        except AttributeError:
            raise HTTPException(status_code=503, detail="Service not initialized")
    
    @app.get("/health/live")
    async def liveness_check():
        """存活检查端点"""
        return {"status": "alive", "service": "orchestrator"}
    
    # 全局异常处理
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        """全局异常处理器"""
        logger.error(
            "Unhandled exception occurred",
            error=str(exc),
            path=request.url.path,
            method=request.method,
            exc_info=True
        )
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )
    
    return app


async def main() -> None:
    """
    主函数，启动服务
    """
    app = create_app()
    
    config = uvicorn.Config(
        app,
        host="0.0.0.0",
        port=settings.service.orchestrator_port,
        log_level=settings.monitoring.log_level.lower(),
        access_log=True,
        reload=settings.is_development,
    )
    
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    asyncio.run(main()) 