#!/usr/bin/env python3
"""
TestGenius 项目状态检查和测试脚本

全面检查项目的实现状态，测试已完成的功能，识别未完成的功能
"""

import asyncio
import sys
import os
import importlib
import traceback
from typing import Dict, List, Any, Optional
from pathlib import Path
import json
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class ProjectStatusChecker:
    """项目状态检查器"""
    
    def __init__(self):
        self.results = {
            "modules": {},
            "services": {},
            "apis": {},
            "functionality": {},
            "issues": [],
            "summary": {}
        }
        
    async def run_full_check(self):
        """运行完整的项目检查"""
        print("🔍 开始TestGenius项目状态检查...")
        print("=" * 60)
        
        # 1. 检查模块导入状态
        await self.check_module_imports()
        
        # 2. 检查配置系统
        await self.check_configuration()
        
        # 3. 检查AI模块
        await self.check_ai_modules()
        
        # 4. 检查加密模块
        await self.check_crypto_modules()
        
        # 5. 检查测试用例生成
        await self.check_test_case_generation()
        
        # 6. 检查Orchestrator服务
        await self.check_orchestrator_service()
        
        # 7. 检查API功能
        await self.check_api_functionality()
        
        # 8. 检查未实现功能
        await self.check_missing_functionality()
        
        # 9. 生成总结报告
        await self.generate_summary()
        
        return self.results
    
    async def check_module_imports(self):
        """检查模块导入状态"""
        print("\n📦 检查模块导入状态...")
        
        modules_to_check = [
            "src.common.config",
            "src.common.logger", 
            "src.orchestrator.service",
            "src.orchestrator.models",
            "src.orchestrator.api",
            "src.ai.llm_client",
            "src.ai.prompt_manager",
            "src.ai.test_case_ai",
            "src.ai.script_gen_ai",
            "src.test_case.generator",
            "src.test_case.models",
            "src.crypto.client",
            "src.crypto.models",
        ]
        
        for module_name in modules_to_check:
            try:
                module = importlib.import_module(module_name)
                self.results["modules"][module_name] = {
                    "status": "✅ 成功",
                    "path": getattr(module, "__file__", "N/A"),
                    "classes": [name for name in dir(module) if name[0].isupper() and not name.startswith('_')],
                }
                print(f"  ✅ {module_name}")
            except Exception as e:
                self.results["modules"][module_name] = {
                    "status": "❌ 失败",
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }
                print(f"  ❌ {module_name}: {e}")
                self.results["issues"].append(f"模块导入失败: {module_name} - {e}")
    
    async def check_configuration(self):
        """检查配置系统"""
        print("\n⚙️ 检查配置系统...")
        
        try:
            from src.common.config import settings
            
            config_info = {
                "version": settings.version,
                "environment": settings.environment,
                "debug": settings.debug,
                "service_port": settings.service.orchestrator_port,
                "log_level": settings.monitoring.log_level,
                "ai_provider": settings.ai.provider,
            }
            
            self.results["services"]["configuration"] = {
                "status": "✅ 正常",
                "config": config_info
            }
            print(f"  ✅ 配置加载成功")
            print(f"     - 版本: {settings.version}")
            print(f"     - 环境: {settings.environment}")
            print(f"     - 端口: {settings.service.orchestrator_port}")
            print(f"     - AI提供商: {settings.ai.provider}")
            
        except Exception as e:
            self.results["services"]["configuration"] = {
                "status": "❌ 失败",
                "error": str(e)
            }
            print(f"  ❌ 配置系统错误: {e}")
            self.results["issues"].append(f"配置系统错误: {e}")
    
    async def check_ai_modules(self):
        """检查AI模块"""
        print("\n🤖 检查AI模块...")
        
        try:
            from src.ai.llm_client import LLMClient, LLMProvider
            from src.ai.prompt_manager import PromptManager
            from src.ai.test_case_ai import TestCaseAI
            
            # 测试LLM客户端
            llm_client = LLMClient()
            await llm_client.initialize(provider=LLMProvider.MOCK)
            
            # 测试提示词管理器
            prompt_manager = PromptManager()
            templates = prompt_manager.list_templates()
            
            # 测试AI测试用例生成器
            test_case_ai = TestCaseAI()
            await test_case_ai.initialize(provider=LLMProvider.MOCK)
            
            self.results["services"]["ai"] = {
                "status": "✅ 正常",
                "llm_client": "已初始化",
                "prompt_templates": len(templates),
                "test_case_ai": "已初始化"
            }
            
            print(f"  ✅ LLM客户端初始化成功")
            print(f"  ✅ 提示词管理器加载了 {len(templates)} 个模板")
            print(f"  ✅ AI测试用例生成器初始化成功")
            
        except Exception as e:
            self.results["services"]["ai"] = {
                "status": "❌ 失败",
                "error": str(e)
            }
            print(f"  ❌ AI模块错误: {e}")
            self.results["issues"].append(f"AI模块错误: {e}")
    
    async def check_crypto_modules(self):
        """检查加密模块"""
        print("\n🔐 检查加密模块...")
        
        try:
            from src.crypto.client import CryptoClient
            from src.crypto.models import CryptoRequest, EncryptionConfig
            
            # 测试加密客户端
            crypto_client = CryptoClient()
            await crypto_client.initialize()
            
            # 测试基本加密功能
            test_data = "Hello, TestGenius!"
            from src.crypto.models import KeyConfig, KeyType
            
            key_config = KeyConfig(
                key_id="test-key",
                key_type=KeyType.SYMMETRIC,
                algorithm="AES-GCM",
                environment="test"
            )
            
            config = EncryptionConfig(
                algorithm="AES-GCM",
                key_config=key_config,
                mode="encrypt"
            )
            
            request = CryptoRequest(
                operation="encrypt",
                data=test_data,
                encryption_config=config
            )
            
            # 注意：这里可能会失败，因为没有真实的密钥
            try:
                response = await crypto_client.process_request(request)
                crypto_status = "✅ 加密测试成功"
            except Exception:
                crypto_status = "⚠️ 加密测试跳过（需要密钥配置）"
            
            self.results["services"]["crypto"] = {
                "status": "✅ 正常",
                "client": "已初始化",
                "test_result": crypto_status
            }
            
            print(f"  ✅ 加密客户端初始化成功")
            print(f"  {crypto_status}")
            
        except Exception as e:
            self.results["services"]["crypto"] = {
                "status": "❌ 失败",
                "error": str(e)
            }
            print(f"  ❌ 加密模块错误: {e}")
            self.results["issues"].append(f"加密模块错误: {e}")
    
    async def check_test_case_generation(self):
        """检查测试用例生成功能"""
        print("\n📝 检查测试用例生成功能...")
        
        try:
            from src.test_case.generator import TestCaseGenerator
            from src.test_case.models import GenerationContext, GenerationOptions
            from src.ai.llm_client import LLMProvider
            
            # 初始化生成器
            generator = TestCaseGenerator()
            await generator.initialize(enable_ai=True, ai_provider=LLMProvider.MOCK)
            
            # 测试生成功能
            requirement_text = "测试用户登录功能"
            
            context = GenerationContext(
                domain="Web应用",
                system_type="登录系统",
                crypto_enabled=True
            )
            
            options = GenerationOptions(
                max_test_cases=3,
                include_boundary_tests=True,
                include_security_tests=True
            )
            
            test_cases = await generator.generate_test_cases(requirement_text, context, options)
            
            self.results["functionality"]["test_case_generation"] = {
                "status": "✅ 正常",
                "generator": "已初始化",
                "test_cases_generated": len(test_cases),
                "ai_enabled": generator.ai_enabled
            }
            
            print(f"  ✅ 测试用例生成器初始化成功")
            print(f"  ✅ 生成了 {len(test_cases)} 个测试用例")
            print(f"  ✅ AI功能状态: {'启用' if generator.ai_enabled else '禁用'}")
            
        except Exception as e:
            self.results["functionality"]["test_case_generation"] = {
                "status": "❌ 失败",
                "error": str(e)
            }
            print(f"  ❌ 测试用例生成错误: {e}")
            self.results["issues"].append(f"测试用例生成错误: {e}")
    
    async def check_orchestrator_service(self):
        """检查Orchestrator服务"""
        print("\n🎭 检查Orchestrator服务...")
        
        try:
            from src.orchestrator.service import OrchestratorService
            from src.orchestrator.models import TestCaseGenerationRequest
            
            # 初始化服务
            service = OrchestratorService()
            await service.initialize()
            
            # 测试会话创建
            session = await service.create_session()
            
            # 测试测试用例生成请求
            request = TestCaseGenerationRequest(
                session_id=session.session_id,
                requirement_text="测试API接口功能",
                context={"api_endpoint": "/api/test"}
            )
            
            response = await service.generate_test_cases(request)
            
            self.results["services"]["orchestrator"] = {
                "status": "✅ 正常",
                "initialized": True,
                "ready": service.is_ready(),
                "session_created": str(session.session_id),
                "test_cases_generated": len(response.test_cases)
            }
            
            print(f"  ✅ Orchestrator服务初始化成功")
            print(f"  ✅ 服务就绪状态: {service.is_ready()}")
            print(f"  ✅ 会话创建成功: {session.session_id}")
            print(f"  ✅ 测试用例生成成功: {len(response.test_cases)} 个用例")
            
            # 清理
            await service.cleanup()
            
        except Exception as e:
            self.results["services"]["orchestrator"] = {
                "status": "❌ 失败",
                "error": str(e)
            }
            print(f"  ❌ Orchestrator服务错误: {e}")
            self.results["issues"].append(f"Orchestrator服务错误: {e}")
    
    async def check_api_functionality(self):
        """检查API功能（通过启动服务器测试）"""
        print("\n🌐 检查API功能...")
        
        try:
            # 这里我们不启动实际的服务器，而是检查API定义
            from src.orchestrator.api import router
            from src.orchestrator.main import create_app
            
            # 创建应用实例
            app = create_app()
            
            # 检查路由
            routes = []
            for route in app.routes:
                if hasattr(route, 'path') and hasattr(route, 'methods'):
                    routes.append({
                        "path": route.path,
                        "methods": list(route.methods) if route.methods else []
                    })
            
            api_routes = [r for r in routes if r["path"].startswith("/api/")]
            
            self.results["apis"]["rest_api"] = {
                "status": "✅ 正常",
                "app_created": True,
                "total_routes": len(routes),
                "api_routes": len(api_routes),
                "routes": api_routes
            }
            
            print(f"  ✅ FastAPI应用创建成功")
            print(f"  ✅ 总路由数: {len(routes)}")
            print(f"  ✅ API路由数: {len(api_routes)}")
            for route in api_routes[:5]:  # 显示前5个API路由
                print(f"     - {route['methods']} {route['path']}")
            
        except Exception as e:
            self.results["apis"]["rest_api"] = {
                "status": "❌ 失败",
                "error": str(e)
            }
            print(f"  ❌ API功能错误: {e}")
            self.results["issues"].append(f"API功能错误: {e}")
    
    async def check_missing_functionality(self):
        """检查未实现的功能"""
        print("\n❓ 检查未实现的功能...")
        
        missing_modules = []
        partial_modules = []
        
        # 检查各个模块的实现状态
        module_status = {
            "script_gen": {"path": "src/script_gen", "name": "脚本生成模块"},
            "scheduler": {"path": "src/scheduler", "name": "执行调度模块"},
            "analysis": {"path": "src/analysis", "name": "结果分析模块"},
            "feedback": {"path": "src/feedback", "name": "反馈闭环模块"},
            "security": {"path": "src/security", "name": "安全合规模块"},
        }
        
        for module_key, module_info in module_status.items():
            module_path = Path(module_info["path"])
            if not module_path.exists():
                missing_modules.append(module_info["name"])
            else:
                # 检查模块是否有基本实现
                files = list(module_path.glob("*.py"))
                if len(files) <= 1:  # 只有__init__.py
                    partial_modules.append(module_info["name"])
        
        # 检查具体功能实现状态
        functionality_status = {
            "实时脚本执行": "❌ 未实现",
            "执行结果分析": "❌ 未实现", 
            "智能反馈处理": "❌ 未实现",
            "安全扫描集成": "❌ 未实现",
            "CI/CD集成": "❌ 未实现",
            "监控告警": "❌ 未实现",
            "数据库持久化": "❌ 未实现",
            "用户认证授权": "❌ 未实现",
            "多租户支持": "❌ 未实现",
            "插件系统": "❌ 未实现"
        }
        
        self.results["functionality"]["missing"] = {
            "missing_modules": missing_modules,
            "partial_modules": partial_modules,
            "functionality_status": functionality_status
        }
        
        print(f"  ❌ 完全缺失的模块 ({len(missing_modules)}个):")
        for module in missing_modules:
            print(f"     - {module}")
            
        print(f"  ⚠️ 部分实现的模块 ({len(partial_modules)}个):")
        for module in partial_modules:
            print(f"     - {module}")
            
        print(f"  ❌ 未实现的功能:")
        for func, status in functionality_status.items():
            print(f"     - {func}: {status}")
    
    async def generate_summary(self):
        """生成总结报告"""
        print("\n📊 生成总结报告...")
        
        # 统计各类状态
        total_modules = len(self.results["modules"])
        successful_modules = len([m for m in self.results["modules"].values() if "✅" in m["status"]])
        
        total_services = len(self.results["services"])
        successful_services = len([s for s in self.results["services"].values() if "✅" in s["status"]])
        
        total_issues = len(self.results["issues"])
        
        # 计算完成度
        completion_percentage = (successful_modules + successful_services) / (total_modules + total_services) * 100
        
        summary = {
            "总体状态": "🟡 部分完成" if completion_percentage > 50 else "🔴 需要修复",
            "完成度": f"{completion_percentage:.1f}%",
            "模块状态": f"{successful_modules}/{total_modules} 成功",
            "服务状态": f"{successful_services}/{total_services} 成功", 
            "问题数量": total_issues,
            "核心功能": {
                "配置系统": "✅" if "configuration" in self.results["services"] and "✅" in self.results["services"]["configuration"]["status"] else "❌",
                "AI模块": "✅" if "ai" in self.results["services"] and "✅" in self.results["services"]["ai"]["status"] else "❌",
                "加密模块": "✅" if "crypto" in self.results["services"] and "✅" in self.results["services"]["crypto"]["status"] else "❌",
                "测试用例生成": "✅" if "test_case_generation" in self.results["functionality"] and "✅" in self.results["functionality"]["test_case_generation"]["status"] else "❌",
                "编排服务": "✅" if "orchestrator" in self.results["services"] and "✅" in self.results["services"]["orchestrator"]["status"] else "❌",
                "API接口": "✅" if "rest_api" in self.results["apis"] and "✅" in self.results["apis"]["rest_api"]["status"] else "❌",
            }
        }
        
        self.results["summary"] = summary
        
        print(f"\n{'='*60}")
        print(f"📋 TestGenius 项目状态总结")
        print(f"{'='*60}")
        print(f"总体状态: {summary['总体状态']}")
        print(f"完成度: {summary['完成度']}")
        print(f"模块状态: {summary['模块状态']}")
        print(f"服务状态: {summary['服务状态']}")
        print(f"问题数量: {summary['问题数量']}")
        
        print(f"\n核心功能状态:")
        for func, status in summary["核心功能"].items():
            print(f"  {status} {func}")
        
        if total_issues > 0:
            print(f"\n⚠️ 需要修复的问题:")
            for i, issue in enumerate(self.results["issues"][:5], 1):
                print(f"  {i}. {issue}")
            if total_issues > 5:
                print(f"  ... 还有 {total_issues - 5} 个问题")


async def main():
    """主函数"""
    checker = ProjectStatusChecker()
    
    try:
        results = await checker.run_full_check()
        
        # 保存结果到文件
        with open("project_status_report.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 详细报告已保存到: project_status_report.json")
        
        return results
        
    except Exception as e:
        print(f"\n❌ 检查过程中发生错误: {e}")
        traceback.print_exc()
        return None


if __name__ == "__main__":
    asyncio.run(main()) 