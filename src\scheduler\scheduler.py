"""
执行调度器

负责管理测试脚本的执行调度、资源分配和并发控制
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Callable
from uuid import UUID, uuid4
from datetime import datetime
from enum import Enum

from src.common.logger import get_logger
from src.orchestrator.models import ExecutionState, ExecutionStatus

logger = get_logger(__name__)


class SchedulerState(str, Enum):
    """调度器状态"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"


class ExecutionScheduler:
    """
    执行调度器
    
    负责管理测试脚本的执行调度、资源分配和并发控制
    """
    
    def __init__(self, model_loader: Optional[Callable] = None):
        self.logger = get_logger(__name__)
        self._initialized = False
        self._state = SchedulerState.IDLE
        self._model_loader = model_loader
        
        # 执行队列和状态管理
        self._execution_queue: asyncio.Queue = asyncio.Queue()
        self._running_executions: Dict[UUID, asyncio.Task] = {}
        self._execution_results: Dict[UUID, Dict[str, Any]] = {}
        
        # 资源管理
        self._max_concurrent_executions = 5
        self._current_executions = 0
        self._execution_semaphore = asyncio.Semaphore(self._max_concurrent_executions)
        
        # 统计信息
        self._total_executions = 0
        self._successful_executions = 0
        self._failed_executions = 0
        
    async def initialize(self) -> None:
        """初始化调度器"""
        try:
            self.logger.info("Initializing ExecutionScheduler")
            
            # 初始化资源池
            await self._initialize_resources()
            
            # 启动调度器主循环
            await self._start_scheduler_loop()
            
            self._initialized = True
            self._state = SchedulerState.RUNNING
            
            self.logger.info("ExecutionScheduler initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize ExecutionScheduler: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.logger.info("Cleaning up ExecutionScheduler")
            
            # 停止调度器
            self._state = SchedulerState.STOPPED
            
            # 等待所有执行完成
            await self._wait_for_all_executions()
            
            # 清理资源
            self._running_executions.clear()
            self._execution_results.clear()
            
            self._initialized = False
            
            self.logger.info("ExecutionScheduler cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error during ExecutionScheduler cleanup: {e}")
    
    async def schedule_execution(
        self,
        execution_id: UUID,
        script_ids: List[UUID],
        execution_options: Dict[str, Any]
    ) -> bool:
        """
        调度执行任务
        
        Args:
            execution_id: 执行ID
            script_ids: 脚本ID列表
            execution_options: 执行选项
            
        Returns:
            bool: 是否成功调度
        """
        if not self._initialized:
            raise RuntimeError("ExecutionScheduler not initialized")
        
        try:
            self.logger.info(f"Scheduling execution {execution_id} with {len(script_ids)} scripts")
            
            # 创建执行任务
            execution_task = {
                "execution_id": execution_id,
                "script_ids": script_ids,
                "options": execution_options,
                "scheduled_at": datetime.now(),
                "status": ExecutionState.QUEUED
            }
            
            # 添加到队列
            await self._execution_queue.put(execution_task)
            
            self.logger.info(f"Execution {execution_id} scheduled successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to schedule execution {execution_id}: {e}")
            return False
    
    async def get_execution_status(self, execution_id: UUID) -> Optional[ExecutionStatus]:
        """获取执行状态"""
        try:
            # 检查是否在运行中
            if execution_id in self._running_executions:
                task = self._running_executions[execution_id]
                if task.done():
                    state = ExecutionState.COMPLETED if not task.exception() else ExecutionState.FAILED
                else:
                    state = ExecutionState.RUNNING
            elif execution_id in self._execution_results:
                result = self._execution_results[execution_id]
                state = result.get("state", ExecutionState.UNKNOWN)
            else:
                return None
            
            # 创建状态对象
            status = ExecutionStatus(
                execution_id=execution_id,
                session_id=uuid4(),  # 这里应该从实际数据中获取
                status=state,
                total_scripts=1,  # 这里应该从实际数据中获取
                completed_scripts=1 if state == ExecutionState.COMPLETED else 0,
                failed_scripts=1 if state == ExecutionState.FAILED else 0,
                start_time=datetime.now(),  # 这里应该从实际数据中获取
                end_time=datetime.now() if state in [ExecutionState.COMPLETED, ExecutionState.FAILED] else None
            )
            
            return status
            
        except Exception as e:
            self.logger.error(f"Failed to get execution status for {execution_id}: {e}")
            return None
    
    async def cancel_execution(self, execution_id: UUID) -> bool:
        """取消执行"""
        try:
            if execution_id in self._running_executions:
                task = self._running_executions[execution_id]
                task.cancel()
                del self._running_executions[execution_id]
                
                self.logger.info(f"Execution {execution_id} cancelled")
                return True
            else:
                self.logger.warning(f"Execution {execution_id} not found for cancellation")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to cancel execution {execution_id}: {e}")
            return False
    
    async def _initialize_resources(self) -> None:
        """初始化资源池"""
        try:
            # 初始化执行环境
            # 这里可以初始化容器、虚拟机等执行环境
            
            self.logger.info("Execution resources initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize resources: {e}")
            raise
    
    async def _start_scheduler_loop(self) -> None:
        """启动调度器主循环"""
        try:
            # 启动后台任务处理队列
            asyncio.create_task(self._process_execution_queue())
            
            self.logger.info("Scheduler loop started")
            
        except Exception as e:
            self.logger.error(f"Failed to start scheduler loop: {e}")
            raise
    
    async def _process_execution_queue(self) -> None:
        """处理执行队列"""
        while self._state == SchedulerState.RUNNING:
            try:
                # 从队列获取任务
                execution_task = await asyncio.wait_for(
                    self._execution_queue.get(), timeout=1.0
                )
                
                # 启动执行任务
                await self._start_execution_task(execution_task)
                
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                self.logger.error(f"Error processing execution queue: {e}")
                await asyncio.sleep(1)
    
    async def _start_execution_task(self, execution_task: Dict[str, Any]) -> None:
        """启动执行任务"""
        execution_id = execution_task["execution_id"]
        
        try:
            # 获取信号量
            async with self._execution_semaphore:
                # 创建执行任务
                task = asyncio.create_task(
                    self._execute_scripts(execution_task)
                )
                
                # 添加到运行中的任务
                self._running_executions[execution_id] = task
                
                # 等待任务完成
                await task
                
                # 清理任务
                if execution_id in self._running_executions:
                    del self._running_executions[execution_id]
                
        except Exception as e:
            self.logger.error(f"Failed to start execution task {execution_id}: {e}")
    
    async def _execute_scripts(self, execution_task: Dict[str, Any]) -> None:
        """执行脚本"""
        execution_id = execution_task["execution_id"]
        script_ids = execution_task["script_ids"]
        
        try:
            self.logger.info(f"Starting execution {execution_id}")
            
            # 模拟脚本执行
            await asyncio.sleep(2)  # 模拟执行时间
            
            # 记录结果
            self._execution_results[execution_id] = {
                "state": ExecutionState.COMPLETED,
                "completed_at": datetime.now(),
                "script_count": len(script_ids),
                "success": True
            }
            
            self._total_executions += 1
            self._successful_executions += 1
            
            self.logger.info(f"Execution {execution_id} completed successfully")
            
        except Exception as e:
            self.logger.error(f"Execution {execution_id} failed: {e}")
            
            # 记录失败结果
            self._execution_results[execution_id] = {
                "state": ExecutionState.FAILED,
                "completed_at": datetime.now(),
                "error": str(e),
                "success": False
            }
            
            self._total_executions += 1
            self._failed_executions += 1
    
    async def _wait_for_all_executions(self) -> None:
        """等待所有执行完成"""
        if self._running_executions:
            self.logger.info(f"Waiting for {len(self._running_executions)} executions to complete")
            
            tasks = list(self._running_executions.values())
            await asyncio.gather(*tasks, return_exceptions=True)
            
            self.logger.info("All executions completed")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_executions": self._total_executions,
            "successful_executions": self._successful_executions,
            "failed_executions": self._failed_executions,
            "current_executions": len(self._running_executions),
            "queue_size": self._execution_queue.qsize(),
            "state": self._state.value
        }
