"""
测试配置模块
"""

def test_basic_import():
    """测试基本导入"""
    from src.common.config import Settings, get_settings
    
    settings = Settings()
    assert settings.version == "0.1.0"
    assert settings.environment == "development"


def test_settings_properties():
    """测试配置属性"""
    from src.common.config import Settings
    
    settings = Settings()
    assert settings.is_development is True
    assert settings.is_production is False 