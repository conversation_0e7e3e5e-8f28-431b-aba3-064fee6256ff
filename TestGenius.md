AI Agent 项目设计文档

本文档旨在使用 Cursor 进行开发，提供清晰、结构化的内容，便于 Cursor 解析与项目成员快速理解。包含项目概述、架构说明、模块规范、加密集成、CI/CD 规范、项目规则等。无示例代码，仅提供结构和流程描述。

0. 文档结构与目录

说明项目目录组织和文档分类，便于团队按需定位：

根目录包含文档目录 (docs/)、源码目录 (src/)、测试目录 (tests/)、CI/CD 配置、部署脚本、配置文件和项目规则文件。

docs/ 下分为概览、架构、模块说明、安全、模板规范说明等子目录。

src/ 下按模块划分子目录，如 Orchestrator、Crypto、测试用例生成、脚本生成、调度、分析、反馈、安全、公共工具等。

tests/ 存放项目自身的单元与集成测试，不包含示例测试代码。

CI/CD 配置文件和部署配置放在相应位置，项目规则和 README 在根目录。

1. 项目概述与目标（docs/overview.md）

背景：描述测试需求的复杂性、现有人工维护成本高、引入 AI Agent 的必要性；强调安全合规场景对加密签名自动化处理的需求。

核心目标：列出自动化需求解析、测试用例与脚本生成、执行调度、结果分析、持续优化闭环；支持接口测试中自动加密/签名/解密/验签；以大模型决策引擎为核心，结合知识检索；可扩展、可部署、多语言、多工具集成。

非功能需求：可扩展性、高可用性与稳定性、性能、安全合规、可维护性和持续优化能力。

使用工具与语言：主要使用 Python 进行 Orchestrator 开发，使用 Rust 或 Go 实现性能关键服务；前端或 IDE 插件使用 TypeScript/Java；测试脚本生成支持 Python、Java、JS/TS 等语言，通过配置驱动。

2. 系统架构说明（docs/architecture.md）

分层与组件：

交互层：Web UI、CLI、IDE 插件

编排层（Orchestrator）：负责接收请求、管理上下文、调用大模型与检索、调度各模块

功能模块：需求解析与知识检索、测试用例生成、脚本生成、执行调度与环境管理、结果分析与报告、Crypto 加密签名服务、反馈闭环、安全合规模块

数据层：关系数据库、NoSQL/缓存、向量数据库、日志存储、监控指标存储

集成层：与 CI/CD、缺陷系统、监控平台、版本控制、项目管理系统对接

安全与运维：身份认证与授权、密钥管理、审计日志、监控告警、运维脚本

部署模式：

本地或单体 MVP 部署

微服务集群部署（Kubernetes + Helm）

混合或私有部署，满足敏感数据和合规需求

多租户 SaaS 部署方案

通信方式：服务间使用 REST 或 gRPC，异步消息使用消息队列；前端与 Orchestrator 使用 HTTPS 或 WebSocket；外部系统集成使用 Webhook 或 API。

扩展与插件机制：

插件注册中心，用于动态加载新的测试工具集成、加密算法扩展或第三方系统连接

配置中心管理项目配置，包括模型策略、加密算法配置、环境区分等

版本管理：提示模板、微调模型、插件版本的管理与回滚

监控与告警：使用 Prometheus + Grafana 监控各服务、加密服务、大模型调用、执行队列等指标，设置告警规则，通过 Slack/Teams/邮件通知。

3. 模块规范

3.1 Orchestrator 模块（docs/modules/orchestrator.md）

职责：

接收并管理用户请求（对话或 API），维护会话上下文

调用大模型与检索系统，执行业务决策和提示生成

调度子模块，包括测试用例生成、脚本生成、执行调度、结果分析、反馈处理

插件加载与管理，根据配置或扩展动态调用不同能力

返回结果或触发执行流程，并收集与处理反馈

实现技术与规范：

使用 Python 开发，框架为 FastAPI + asyncio

编排使用 LangChain + LangGraph，定义工具函数接口和决策链

配置管理使用 pydantic，支持环境变量和 Vault 获取敏感配置

日志记录结构化 JSON，包含请求 ID、步骤耗时、模块名称、状态和错误信息；设置合理日志级别

错误处理和重试机制，对可重试操作使用指数退避重试；出现关键异常时提供降级或告警

插件机制：

定义插件接口协议，插件实现需声明功能点

插件目录统一管理，Orchestrator 启动时扫描并加载

支持插件热插拔、版本管理与隔离

3.2 Crypto 模块（docs/modules/crypto.md）

职责：

提供统一加密/解密、签名/验签、哈希等功能接口

支持多种算法：AES-GCM、3DES、RSA2、SHA256、SM2、SM3、SM4、MD5（仅限校验用途）

集成 Vault/KMS 实现密钥获取、缓存、轮换与审计

以 Python-Rust 扩展或独立微服务形式提供服务

实现与规范：

核心实现使用 Rust，利用成熟加密库并关注安全更新；通过 PyO3 或微服务暴露给 Python 和其他语言调用

密钥管理：在运行时通过 Vault 安全获取，对称和非对称密钥对不明文存储在代码或配置中；支持密钥轮换兼容

性能优化：对称操作并行处理大数据，非对称仅用于签名小数据；服务部署支持水平扩展并监控延迟/失败率

接口规范：统一调用参数结构，明确算法名称、keyId、数据格式（如编码要求）、nonce/timestamp 处理方式；验证环境配置与协议一致

安全要求：服务仅在受信网络或通过 mTLS 保护；调用方需经过授权；日志中避免敏感明文输出，仅记录必要元信息

3.3 测试用例生成模块（docs/modules/test_case.md）

职责：

基于需求文本和上下文，通过大模型和检索结果抽取功能点、边界条件和异常场景，进行风险评估

生成结构化用例定义，包括场景描述、前置条件、测试步骤、期望结果、测试数据要求及加密签名需求标签

生成测试数据方案：随机、边界、异常数据；对敏感字段进行脱敏或使用合成数据

覆盖优化：结合历史覆盖或变更信息过滤冗余并补充遗漏场景

导出用例定义，供脚本生成模块使用

实现与规范：

Prompt 构造与检索增强：将相关文档或历史用例作为上下文提供给大模型；使用模板化提示

用例定义格式：用 JSON/YAML 等结构化格式描述，包含必要字段和元信息

风险评估依据：基于历史缺陷统计或模型推断，标注高风险场景优先生成

输出校验：对生成的用例定义进行格式和逻辑校验，确保字段完整性和一致性

3.4 脚本生成模块（docs/modules/script_gen.md）

职责：

将结构化用例定义转化为可执行测试脚本，不包含具体示例代码，仅描述流程和调用点

支持多语言脚本模板（Python、Java、JS/TS 等），通过配置驱动选择目标语言和测试框架

在脚本中插入加密签名、发送请求、响应处理、验签解密、断言验证、异常处理和日志埋点的调用逻辑

生成依赖声明和环境配置说明，指导如何配置密钥访问和测试运行环境

支持并行/异步执行流程说明，以及 Mock 服务集成和本地验证流程说明

实现与规范：

模板管理：维护模块化模板片段描述位置，不包含具体代码示例，仅说明占位逻辑和调用接口

配置替换：根据用例定义和接口元数据替换模板占位符，生成脚本文件

环境依赖说明：描述需要安装或启动的服务，以及环境变量或 Vault 配置要求

错误与重试逻辑：在脚本流程说明中标注如何处理加解密或网络调用异常及重试策略

安全流程：在模板说明中标注加密参数、签名字段、nonce/timestamp 处理方式和配置校验流程

3.5 执行调度模块（docs/modules/scheduler.md）

职责：

接收执行请求，分发测试任务到执行节点

管理测试环境部署，包括被测服务、依赖、Mock 服务和 Crypto 服务（或扩展库）

管理资源与并发，支持动态扩缩容和队列优先管理

收集执行日志和监控指标，实时推送至集中日志系统或监控平台

将执行结果回传给 Orchestrator 以便进一步分析

实现与规范：

初期可用 Python+asyncio 实现，后期高并发可使用 Go 或 Rust 实现调度逻辑描述

环境隔离与配置注入说明：明确使用容器或虚拟化方式部署测试环境，并通过配置中心或 Vault 提供运行所需凭证和配置

并发控制规范：描述队列长度监控、失败重试、资源配额控制、故障隔离流程

日志和指标规范：记录执行开始结束时间、状态、加密调用日志元信息、失败原因；发送至集中系统

通信协议：说明与 Orchestrator 交互方式和回传机制，无示例代码，仅流程说明

3.6 结果分析模块（docs/modules/analysis.md）

职责：

解析执行日志，特别关注加密/签名流程中的异常或失败

利用大模型辅助分析失败根因，结合日志、堆栈信息、监控指标生成排查思路

生成测试报告说明，包括执行概览、失败详情、加密流程健康状况、性能指标、安全扫描结果等，并提供高层次改进建议

与缺陷系统集成，说明创建或更新缺陷的流程和必要信息

提供仪表板或可视化说明，以便团队监控关键指标和趋势

实现与规范：

日志解析规范：描述如何结构化解析日志字段、提取关键信息，不包含示例代码

报告格式规范：定义报告结构和必含部分，如执行统计、失败原因分析、加密流程验证结果、趋势图描述、改进建议部分

集成流程说明：与缺陷系统对接流程、必需字段、状态跟踪方式

可视化指标说明：说明需要展示的关键指标类型及监控平台配置思路

3.7 反馈闭环模块（docs/modules/feedback.md）

职责：

收集测试脚本执行后的人工修改或反馈信息，用于改进生成逻辑

基于反馈调整 Prompt 片段或微调本地模型，优化用例和脚本生成效果

更新知识库，记录兼容性经验、最佳实践、加密协议适配方案，以便检索增强

支持跨项目经验迁移，描述元数据采集和匹配流程

实现与规范：

反馈采集流程：说明通过版本控制系统或反馈接口获取脚本修改记录和反馈标签

反馈处理流程：说明如何分析反馈、更新提示模板或触发微调任务的流程

知识库管理：描述经验条目结构、存储与检索方式、生命周期管理

跨项目迁移说明：如何使用项目元信息（领域、技术栈、安全需求）匹配历史经验

3.8 安全合规模块（docs/modules/security.md）

职责：

身份认证与授权管理方案，包括 OAuth/OIDC、JWT 签发与验证、RBAC、多租户隔离说明

数据脱敏与隐私保护说明，包括调用大模型前脱敏流程、日志加密或脱敏存储策略、访问控制说明

审计日志方案，描述记录模型调用、加密签名操作、测试执行和缺陷创建的审计需求和存储管理

算法合规说明，列出支持算法及其使用场景，定期校验与更新策略

安全扫描与渗透测试集成流程说明，在 CI/CD 中的执行节点和报告处理机制

实现与规范：

认证授权流程说明：描述用户凭证管理、令牌生命周期、权限校验流程，不包含代码

脱敏与隐私流程：说明敏感字段识别、脱敏策略、数据加密存储与访问控制流程

审计日志设计：定义应记录内容、日志保护措施（如签名或哈希链）、存储与查询流程

合规流程：定期算法评审、依赖库安全审计、合规检查流程

安全测试集成：说明在 CI/CD 中如何集成静态/动态扫描、如何处理报告和反馈

4. 模板与说明规范（docs/templates/）

4.1 模板管理原则

模板仅描述结构和占位逻辑，不包含具体代码示例

多语言支持说明：指明需生成哪种语言脚本，并说明需插入哪些调用点和配置项

安全流程占位：描述加密调用、签名调用、解密与验签调用的步骤和所需配置元素

配置说明：说明依赖环境变量、Vault 配置、服务地址等，不包含详细代码

版本管理：模板变化需记录变更原因与影响，存入版本控制

4.2 Prompt 片段管理

组织 Prompt 片段库，按功能分类，如需求解析、用例生成、脚本生成、加密流程、错误处理等

Prompt 片段描述格式清晰，包含占位字段说明和上下文注入方式，不含示例代码

提供 Prompt 校验工具流程，检查占位字段与实际配置一致性

4.3 CI/CD 配置说明

描述 CI/CD 流程各阶段职责：代码检查、依赖安装、格式和安全检查、单元测试、构建、部署测试环境、集成测试（含安全流程验证）、报告归档、通知和发布

说明如何在 CI 环境中安全获取 Vault 访问令牌，如何注入环境变量，不包含具体脚本

回滚和审批流程说明，环境区分管理流程

4.4 部署与容器化说明

说明 Orchestrator 和 CryptoService 部署方式：使用容器化平台，配置资源限制与健康检查，不含具体 Dockerfile 或 Helm 模板

描述配置注入流程：环境变量、Secret 管理、Vault 集成说明

提示监控部署架构思路，包括多实例、负载均衡、故障恢复，不含具体命令

4.5 Vault/KMS 集成说明

说明密钥存储和访问流程：Vault 引擎类型、策略配置思路、开发与生产环境差异

描述本地开发模拟与 CI 环境令牌获取说明，不含具体命令示例

密钥轮换流程设计：版本兼容性、旧数据解密、文档记录要求

5. 项目规则（docs/project_rules.md）

本节提供详细项目规则和规范，便于团队统一开发流程和质量保障，适合 Cursor 解析，无代码示例。

5.1 开发环境与工具

Python 版本与虚拟环境：使用规定 Python 版本，通过内部工具管理虚拟环境；团队统一方式创建和激活

依赖管理工具：使用自定义 CLI 包管理（如 uv），涵盖添加、移除、更新、安装和安全检查命令；明确开发与生产依赖区别

编辑与格式化流程：统一使用格式化和静态检查工具，通过预提交钩子和 CI 强制执行；类型检查可选

异步与框架：服务开发使用异步框架，避免阻塞；耗时任务通过异步或消息队列处理

版本控制与提交规范：Git 分支模型、PR 评审流程、提交信息规范（Conventional Commits），通过自动化工具验证

项目初始化脚手架：提供命令初始化目录和基础配置，确保一致性

5.2 依赖与包管理规范

包管理命令：列明管理命令作用与流程，无示例代码，仅说明使用方式

依赖分组：开发、测试、生产依赖分类说明；CI 环境只安装生产依赖

多环境支持：配置文件或环境变量区分不同环境依赖或功能开关

安全检查：集成依赖漏洞扫描流程，说明检查方式和报告处理流程

5.3 代码规范与质量保障

格式化与静态检查：说明工具及执行时机，通过 CI 强制校验

类型注解与检查：可选说明，提高可维护性

日志与监控埋点规范：说明日志格式、关键字段、敏感信息处理原则

异常处理与重试：说明异常类型分类、重试策略设计、降级方案和日志记录原则

5.4 安全与密钥管理

密钥存储与访问：描述 Vault/KMS 集成思路、访问控制策略、开发与生产环境流程

加密签名策略：说明默认与可选算法、配置开关、流程校验要求

配置管理：敏感配置通过环境或 Vault 动态加载；使用配置模型验证格式和完整性

安全扫描与审计：集成依赖和代码安全扫描流程，审计日志设计和存储要求

5.5 加密签名集成流程

接口加密需求配置：说明如何在接口规范中标注加密需求或使用独立配置文件，无示例代码说明字段含义和验证流程

初始化与验证流程：说明 CryptoClient 初始化逻辑、环境校验流程和协议一致性验证

测试脚本生成流程：描述生成流程中应插入的加密签名调用步骤及异常处理策略

Mock 与本地验证流程：说明如何在本地或 CI 环境验证加密流程一致性，无示例命令，仅流程说明

5.6 脚本生成与模板管理

模板结构与管理：描述模板组织方式和占位逻辑，不包含示例内容

Prompt 片段管理：说明 Prompt 片段分类和校验流程

脚本生成流程说明：描述从用例定义到脚本文件生成的流程及必要配置注入

本地验证流程：说明如何执行生成脚本进行验证，无示例命令，仅说明依赖和环境要求

5.7 CI/CD 规范

流程阶段说明：描述各阶段功能和职责：代码检查、安全扫描、测试、构建、部署、集成测试、报告和通知、发布与回滚

Secrets 管理：说明如何在 CI 环境安全获取密钥访问令牌，不包含具体平台配置示例

环境隔离与审批流程：说明不同环境部署流程和审批策略

5.8 监控与告警规范

监控指标类别：列举需要监控的关键指标类型，无具体配置示例，仅说明采集思路

日志收集与存储：说明日志集中化方案、敏感数据处理原则

告警规则设计：描述常见告警场景和处理流程，通知渠道说明

5.9 发布与版本管理

版本号与发布策略：语义化版本管理流程说明、发布阶段说明和文档更新要求

回滚与兼容性：说明回滚流程和兼容性验证需求

发布通知与文档同步：描述发布后通知团队和更新文档的流程

5.10 文档与培训规范

文档结构与格式：结构化 Markdown 规范、标题层级、元信息管理

示例与教程：说明示例项目、教程内容类型和更新流程，无示例内容

培训与支持：描述培训计划和反馈渠道

5.11 反馈与持续优化

反馈收集流程：说明通过 Issue 或工具收集反馈和记录格式

优化流程：描述基于反馈更新 Prompt 片段或模型微调的标准流程

知识库管理：说明经验条目结构、存储与检索流程、生命周期管理

跨项目迁移说明：描述项目元信息采集与经验匹配流程

5.12 里程碑与迭代计划

MVP 及阶段目标：说明每个阶段主要实现内容和预期成果，无时间表示例，仅流程和内容说明

迭代节奏与评审：描述迭代周期和回顾流程

5.13 风险与防范措施

大模型依赖风险：说明延迟、成本、安全隐私应对策略

加密兼容风险：描述协议一致性验证流程和性能优化思路

环境配置风险：说明本地模拟和 CI 预检测流程

团队接受风险：描述文档、培训和支持方案

6. Cursor 友好格式说明

结构化 Markdown，明确标题层级，便于 Cursor 快速定位和提炼要点

模块化文件组织，每个功能模块或规范单独文档

说明性文本代替示例代码，仅描述流程和调用点

可选 YAML frontmatter 提供元信息（如 title、author、updated）

文档与代码功能保持同步，变更后及时更新文档

维护文档索引文件（如 SUMMARY.md），便于导航

7. 下一步

创建目录结构和空文档文件，分类放置各部分内容

将设计细节填写至各 Markdown 文件，根据规范完善流程描述

确定并发布项目规则，与团队达成一致

实现内部工具（uv）命令，管理环境与依赖、配置校验、Prompt 校验、加密流程验证等

开发 Crypto 模块并集成 Vault，验证流程一致性

在 Orchestrator 集成决策与生成流程，并进行本地验证

配置 CI/CD 流程，确保规则和流程在自动化环境中执行

部署监控与告警，收集初期指标并调整

持续收集反馈，完善 Prompt 片段、流程文档和知识库

