#!/usr/bin/env python3
"""
简单的API测试脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查"""
    print("=== 健康检查测试 ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"健康检查: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_create_session():
    """测试创建会话"""
    print("\n=== 创建会话测试 ===")
    try:
        response = requests.post(f"{BASE_URL}/api/v1/sessions")
        print(f"创建会话: {response.status_code}")
        if response.status_code == 200:
            session_data = response.json()
            print(f"会话ID: {session_data['session_id']}")
            return session_data['session_id']
        else:
            print(f"错误响应: {response.text}")
            return None
    except Exception as e:
        print(f"创建会话失败: {e}")
        return None

def test_generate_test_cases(session_id):
    """测试生成测试用例"""
    print("\n=== 测试用例生成测试 ===")
    
    request_data = {
        "session_id": session_id,
        "requirement_text": "用户登录系统，需要支持用户名密码登录，登录后可以查看个人信息",
        "context": {
            "domain": "Web应用",
            "system_type": "用户管理系统"
        },
        "generation_options": {
            "max_test_cases": 5
        },
        "crypto_enabled": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/test-cases/generate",
            json=request_data,
            timeout=30
        )
        print(f"生成测试用例: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"任务ID: {result['task_id']}")
            print(f"生成测试用例数量: {len(result['test_cases'])}")
            
            # 显示第一个测试用例
            if result['test_cases']:
                first_case = result['test_cases'][0]
                print(f"第一个测试用例:")
                print(f"  标题: {first_case['title']}")
                print(f"  描述: {first_case['description']}")
                print(f"  优先级: {first_case['priority']}")
                print(f"  标签: {first_case['tags']}")
            
            return result['test_cases']
        else:
            print(f"错误响应: {response.text}")
            return []
    except Exception as e:
        print(f"生成测试用例失败: {e}")
        return []

def main():
    """主测试函数"""
    print("TestGenius API 简单测试")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"服务地址: {BASE_URL}")
    
    # 1. 健康检查
    if not test_health():
        print("服务未启动或不可用")
        return
    
    # 2. 创建会话
    session_id = test_create_session()
    if not session_id:
        print("无法创建会话")
        return
    
    # 3. 生成测试用例
    test_cases = test_generate_test_cases(session_id)
    
    print(f"\n=== 测试完成 ===")
    print(f"成功生成 {len(test_cases)} 个测试用例")

if __name__ == "__main__":
    main() 