"""
简化的日志模块
"""

import logging
import sys

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        stream=sys.stdout
    )

def get_logger(name=None):
    """获取日志记录器"""
    if name is None:
        import inspect
        frame = inspect.currentframe()
        try:
            caller_frame = frame.f_back
            name = caller_frame.f_globals.get('__name__', 'unknown')
        finally:
            del frame
    
    return logging.getLogger(name)

# 初始化日志
setup_logging() 