"""
AI驱动的脚本生成器

使用大模型生成测试脚本代码
"""

from typing import Dict, List, Optional, Any
import json
import asyncio

from src.ai.llm_client import LLMClient, LLMProvider
from src.ai.prompt_manager import PromptManager, PromptType
from src.test_case.models import TestCaseDefinition
from src.common.logger import get_logger

logger = get_logger(__name__)


class ScriptGenAI:
    """
    AI驱动的脚本生成器
    
    根据测试用例生成各种语言的测试脚本
    """
    
    def __init__(self):
        self.llm_client = LLMClient()
        self.prompt_manager = PromptManager()
        self.is_initialized = False
        
        # 支持的语言和框架
        self.supported_languages = {
            'python': ['pytest', 'unittest', 'requests'],
            'java': ['testng', 'junit', 'rest-assured'],
            'javascript': ['jest', 'mocha', 'playwright'],
            'typescript': ['jest', 'playwright', 'cypress']
        }
    
    async def initialize(
        self,
        provider: LLMProvider = LLMProvider.MOCK,
        model_name: str = "gpt-3.5-turbo",
        **kwargs
    ) -> None:
        """
        初始化脚本生成AI
        
        Args:
            provider: LLM提供商
            model_name: 模型名称
            **kwargs: 其他配置参数
        """
        try:
            logger.info("Initializing ScriptGenAI")
            
            await self.llm_client.initialize(
                provider=provider,
                model_name=model_name,
                **kwargs
            )
            
            self.is_initialized = True
            logger.info("ScriptGenAI initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize ScriptGenAI: {e}")
            raise
    
    async def generate_script(
        self,
        test_case: TestCaseDefinition,
        language: str = "python",
        framework: str = "pytest",
        crypto_enabled: bool = False,
        environment: str = "test",
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成测试脚本
        
        Args:
            test_case: 测试用例定义
            language: 目标语言
            framework: 测试框架
            crypto_enabled: 是否启用加密
            environment: 执行环境
            **kwargs: 其他配置
            
        Returns:
            生成的脚本信息
        """
        if not self.is_initialized:
            logger.warning("ScriptGenAI not initialized, using template generation")
            return await self._template_generation(test_case, language, framework, crypto_enabled)
        
        try:
            logger.info(
                "Generating script with AI",
                language=language,
                framework=framework,
                crypto_enabled=crypto_enabled
            )
            
            # 准备测试用例JSON
            test_case_json = {
                'title': test_case.title,
                'description': test_case.description,
                'type': test_case.type,
                'priority': test_case.priority,
                'tags': test_case.tags,
                'steps': test_case.steps,
                'expected_result': test_case.expected_result,
                'test_data': test_case.test_data,
                'crypto_requirements': test_case.crypto_requirements
            }
            
            # 准备提示词变量
            prompt_variables = {
                'test_case': json.dumps(test_case_json, ensure_ascii=False, indent=2),
                'language': language,
                'framework': framework,
                'crypto_enabled': crypto_enabled,
                'environment': environment
            }
            
            # 获取提示词
            system_prompt = self.prompt_manager.get_system_prompt(PromptType.SCRIPT_GENERATION)
            user_prompt = self.prompt_manager.get_prompt(
                PromptType.SCRIPT_GENERATION,
                prompt_variables
            )
            
            # 调用LLM生成
            response = await self.llm_client.chat(
                messages=[{'role': 'user', 'content': user_prompt}],
                system_prompt=system_prompt
            )
            
            # 解析生成的脚本
            script_info = await self._parse_script_response(
                response['content'],
                test_case,
                language,
                framework
            )
            
            script_info.update({
                'model': response.get('model', 'unknown'),
                'usage': response.get('usage', {}),
                'generation_method': 'ai'
            })
            
            logger.info(
                "AI script generation completed",
                language=language,
                lines=len(script_info.get('code', '').split('\n'))
            )
            
            return script_info
            
        except Exception as e:
            logger.error(f"AI script generation failed: {e}")
            # 降级到模板生成
            return await self._template_generation(test_case, language, framework, crypto_enabled)
    
    async def _parse_script_response(
        self,
        content: str,
        test_case: TestCaseDefinition,
        language: str,
        framework: str
    ) -> Dict[str, Any]:
        """
        解析AI生成的脚本响应
        
        Args:
            content: AI响应内容
            test_case: 测试用例
            language: 编程语言
            framework: 测试框架
            
        Returns:
            脚本信息字典
        """
        # 提取代码块
        code = self._extract_code_block(content, language)
        
        # 生成文件名
        filename = self._generate_filename(test_case, language)
        
        # 提取依赖
        dependencies = self._extract_dependencies(code, language, framework)
        
        # 提取配置
        config = self._extract_config(content)
        
        return {
            'code': code,
            'filename': filename,
            'language': language,
            'framework': framework,
            'dependencies': dependencies,
            'config': config,
            'test_case_id': test_case.title,
            'description': f"Auto-generated test script for: {test_case.title}"
        }
    
    def _extract_code_block(self, content: str, language: str) -> str:
        """从AI响应中提取代码块"""
        lines = content.split('\n')
        code_lines = []
        in_code_block = False
        
        for line in lines:
            line_stripped = line.strip()
            
            # 检测代码块开始
            if line_stripped.startswith('```'):
                if language.lower() in line_stripped.lower() or not in_code_block:
                    in_code_block = not in_code_block
                    continue
            
            # 收集代码行
            if in_code_block:
                code_lines.append(line)
        
        # 如果没有找到代码块，尝试提取缩进的代码
        if not code_lines:
            for line in lines:
                if line.startswith('    ') or line.startswith('\t'):
                    code_lines.append(line)
        
        return '\n'.join(code_lines)
    
    def _generate_filename(self, test_case: TestCaseDefinition, language: str) -> str:
        """生成脚本文件名"""
        # 清理标题作为文件名
        import re
        clean_title = re.sub(r'[^\w\s-]', '', test_case.title)
        clean_title = re.sub(r'[-\s]+', '_', clean_title).lower()
        
        # 添加语言扩展名
        extensions = {
            'python': '.py',
            'java': '.java',
            'javascript': '.js',
            'typescript': '.ts'
        }
        
        extension = extensions.get(language, '.txt')
        return f"test_{clean_title}{extension}"
    
    def _extract_dependencies(self, code: str, language: str, framework: str) -> List[str]:
        """提取代码依赖"""
        dependencies = []
        
        if language == 'python':
            import re
            # 提取import语句
            imports = re.findall(r'import\s+(\w+)', code)
            from_imports = re.findall(r'from\s+(\w+)', code)
            dependencies.extend(imports + from_imports)
            
            # 添加框架依赖
            if framework == 'pytest':
                dependencies.append('pytest')
            elif framework == 'unittest':
                dependencies.append('unittest')
            
            # 常见依赖
            if 'requests' in code:
                dependencies.append('requests')
            if 'json' in code:
                dependencies.append('json')
        
        elif language == 'java':
            if framework == 'testng':
                dependencies.append('org.testng:testng')
            elif framework == 'junit':
                dependencies.append('junit:junit')
        
        return list(set(dependencies))
    
    def _extract_config(self, content: str) -> Dict[str, Any]:
        """提取配置信息"""
        config = {}
        
        # 提取环境变量
        import re
        env_vars = re.findall(r'os\.environ\[[\'"](.*?)[\'"]\]', content)
        if env_vars:
            config['environment_variables'] = env_vars
        
        # 提取URL配置
        urls = re.findall(r'https?://[^\s]+', content)
        if urls:
            config['base_urls'] = urls
        
        return config
    
    async def _template_generation(
        self,
        test_case: TestCaseDefinition,
        language: str,
        framework: str,
        crypto_enabled: bool
    ) -> Dict[str, Any]:
        """
        模板生成方法（降级方案）
        
        Args:
            test_case: 测试用例
            language: 编程语言
            framework: 测试框架
            crypto_enabled: 是否启用加密
            
        Returns:
            生成的脚本信息
        """
        logger.info("Using template-based script generation")
        
        if language == 'python' and framework == 'pytest':
            code = self._generate_python_pytest_template(test_case, crypto_enabled)
        elif language == 'java' and framework == 'testng':
            code = self._generate_java_testng_template(test_case, crypto_enabled)
        else:
            code = self._generate_generic_template(test_case, language, framework)
        
        filename = self._generate_filename(test_case, language)
        dependencies = self._extract_dependencies(code, language, framework)
        
        return {
            'code': code,
            'filename': filename,
            'language': language,
            'framework': framework,
            'dependencies': dependencies,
            'config': {},
            'test_case_id': test_case.title,
            'description': f"Template-generated test script for: {test_case.title}",
            'generation_method': 'template'
        }
    
    def _generate_python_pytest_template(self, test_case: TestCaseDefinition, crypto_enabled: bool) -> str:
        """生成Python pytest模板"""
        crypto_import = ""
        crypto_setup = ""
        crypto_calls = ""
        
        if crypto_enabled:
            crypto_import = "from src.crypto.client import CryptoClient"
            crypto_setup = """
    @pytest.fixture
    def crypto_client():
        client = CryptoClient()
        client.initialize()
        return client"""
            crypto_calls = """
        # 加密请求数据
        if hasattr(self, 'crypto_client'):
            encrypted_data = await self.crypto_client.encrypt(test_data)
        else:
            encrypted_data = test_data"""
        
        return f'''"""
{test_case.description}

测试用例: {test_case.title}
类型: {test_case.type}
优先级: {test_case.priority}
"""

import pytest
import requests
import json
{crypto_import}


class Test{test_case.title.replace(' ', '').replace('-', '')}:
    """测试类: {test_case.title}"""
    
    base_url = "http://localhost:8000"
    test_data = {json.dumps(test_case.test_data, ensure_ascii=False, indent=8)}
{crypto_setup}
    
    def test_{test_case.title.lower().replace(' ', '_').replace('-', '_')}(self):
        """
        测试方法: {test_case.title}
        
        测试步骤:
{chr(10).join([f"        {step}" for step in test_case.steps])}
        
        预期结果: {test_case.expected_result}
        """
        # 测试数据准备
        test_data = self.test_data.copy()
{crypto_calls}
        
        # 执行测试步骤
        try:
            # TODO: 实现具体的测试步骤
{chr(10).join([f"            # {step}" for step in test_case.steps])}
            
            # 发送请求 (示例)
            response = requests.post(
                f"{{self.base_url}}/api/test",
                json=test_data,
                timeout=30
            )
            
            # 验证响应
            assert response.status_code == 200
            result = response.json()
            
            # 验证预期结果
            # TODO: 根据具体需求添加断言
            assert "success" in result
            
        except Exception as e:
            pytest.fail(f"测试执行失败: {{e}}")
    
    def teardown_method(self):
        """测试清理"""
        # TODO: 添加清理逻辑
        pass
'''
    
    def _generate_java_testng_template(self, test_case: TestCaseDefinition, crypto_enabled: bool) -> str:
        """生成Java TestNG模板"""
        return f'''
/**
 * {test_case.description}
 * 
 * 测试用例: {test_case.title}
 * 类型: {test_case.type}
 * 优先级: {test_case.priority}
 */

import org.testng.annotations.*;
import org.testng.Assert;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import java.util.HashMap;
import java.util.Map;

public class Test{test_case.title.replace(' ', '').replace('-', '')} {{
    
    private String baseUrl = "http://localhost:8000";
    private Map<String, Object> testData;
    
    @BeforeMethod
    public void setUp() {{
        // 初始化测试数据
        testData = new HashMap<>();
        {chr(10).join([f'        testData.put("{k}", "{v}");' for k, v in test_case.test_data.items()])}
    }}
    
    @Test(description = "{test_case.description}")
    public void test{test_case.title.replace(' ', '').replace('-', '')}() {{
        try {{
            // 测试步骤:
{chr(10).join([f"            // {step}" for step in test_case.steps])}
            
            // 发送请求
            Response response = RestAssured
                .given()
                    .contentType("application/json")
                    .body(testData)
                .when()
                    .post(baseUrl + "/api/test")
                .then()
                    .statusCode(200)
                    .extract().response();
            
            // 验证响应
            String responseBody = response.getBody().asString();
            Assert.assertTrue(responseBody.contains("success"), "响应应包含success字段");
            
            // TODO: 添加更多断言
            
        }} catch (Exception e) {{
            Assert.fail("测试执行失败: " + e.getMessage());
        }}
    }}
    
    @AfterMethod
    public void tearDown() {{
        // 清理资源
    }}
}}
'''
    
    def _generate_generic_template(self, test_case: TestCaseDefinition, language: str, framework: str) -> str:
        """生成通用模板"""
        return f'''
// {test_case.description}
// 测试用例: {test_case.title}
// 语言: {language}
// 框架: {framework}

/*
测试步骤:
{chr(10).join([f"  {i+1}. {step}" for i, step in enumerate(test_case.steps)])}

预期结果: {test_case.expected_result}

测试数据: {json.dumps(test_case.test_data, ensure_ascii=False, indent=2)}
*/

// TODO: 实现 {language} {framework} 测试脚本
// 请根据上述测试用例信息完善具体实现
'''
    
    def get_supported_languages(self) -> Dict[str, List[str]]:
        """获取支持的语言和框架"""
        return self.supported_languages.copy()
    
    def is_language_supported(self, language: str, framework: str = None) -> bool:
        """检查语言和框架是否支持"""
        if language not in self.supported_languages:
            return False
        
        if framework:
            return framework in self.supported_languages[language]
        
        return True
    
    def is_available(self) -> bool:
        """检查脚本生成器是否可用"""
        return self.is_initialized and self.llm_client.is_available() 