# TestGenius 环境配置示例
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 基础配置
# =============================================================================
ENVIRONMENT=development
DEBUG=true
VERSION=0.1.0

# =============================================================================
# 服务配置
# =============================================================================
SERVICE_HOST=0.0.0.0
SERVICE_ORCHESTRATOR_PORT=8000
SERVICE_WORKERS=1

# =============================================================================
# 安全配置
# =============================================================================
# 生产环境必须更改此密钥！
SECURITY_SECRET_KEY=dev-secret-key-change-in-production
# 允许的CORS来源，生产环境不要使用 "*"
SECURITY_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
# API密钥头名称
SECURITY_API_KEY_HEADER=X-API-Key
# 每分钟请求限制
SECURITY_RATE_LIMIT_PER_MINUTE=60

# =============================================================================
# AI配置
# =============================================================================
# AI提供商: openai, azure, ollama, mock
AI_PROVIDER=mock
# AI API密钥 (根据提供商设置)
AI_API_KEY=
# 模型名称
AI_MODEL_NAME=gpt-3.5-turbo
# 最大令牌数
AI_MAX_TOKENS=4000
# 温度参数 (0.0-1.0)
AI_TEMPERATURE=0.7

# =============================================================================
# 数据库配置 (未来版本)
# =============================================================================
# 主数据库URL
DATABASE_URL=
# Redis缓存URL
DATABASE_REDIS_URL=redis://localhost:6379

# =============================================================================
# 监控配置
# =============================================================================
# 日志级别: DEBUG, INFO, WARNING, ERROR
MONITORING_LOG_LEVEL=INFO
# 启用指标收集
MONITORING_ENABLE_METRICS=true
# 指标端口
MONITORING_METRICS_PORT=9090

# =============================================================================
# 开发配置
# =============================================================================
# 开发模式下的额外配置
DEV_AUTO_RELOAD=true
DEV_SHOW_DOCS=true

# =============================================================================
# 加密模块配置
# =============================================================================
# CryptoClient运行模式：production（强制使用Vault）或dev（允许开发环境回退）
CRYPTO_MODE=dev
