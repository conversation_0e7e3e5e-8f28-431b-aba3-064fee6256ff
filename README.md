# TestGenius AI Agent

智能化测试用例生成与执行平台，集成加密签名能力的AI驱动测试自动化解决方案。

[![Python Version](https://img.shields.io/badge/python-3.9%2B-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104%2B-green.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Security](https://img.shields.io/badge/security-enhanced-brightgreen.svg)](#security-features)

## 项目概述

TestGenius是一个基于大模型的AI Agent项目，旨在自动化测试需求解析、用例生成、脚本生成和执行调度。特别支持在安全合规场景下的接口测试中自动处理加密/签名/解密/验签流程。

**🎯 当前版本**: v0.1.0 (MVP阶段) | **📈 完成度**: ~70%

## ✨ 核心功能

### 🚀 已实现功能
- 🤖 **智能需求解析**: 基于大模型理解需求文档，自动提取测试要点
- 📋 **测试用例生成**: 自动生成结构化测试用例，覆盖正常、边界和异常场景
- 🔐 **加密签名框架**: 统一的加密/解密、签名/验签接口（支持多种算法）
- 🌐 **RESTful API**: 完整的异步API服务，支持会话管理和任务调度
- 🛡️ **安全增强**: 输入验证、速率限制、安全审计和配置验证
- 📊 **风险评估**: AI驱动的测试用例风险分析和优先级排序

### 🚧 开发中功能
- 🔧 **多语言脚本生成**: 支持Python、Java、JS/TS等多种语言的测试脚本生成
- ⚡ **执行调度**: 分布式测试执行，支持并发和环境隔离
- 📊 **结果分析**: AI辅助的失败分析和报告生成
- 🔄 **反馈闭环**: 持续学习优化，知识库管理

## 🏗️ 技术架构

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   API Gateway   │    │  Orchestrator   │
│                 │◄──►│                 │◄──►│    Service      │
│  React/Vue.js   │    │   FastAPI       │    │   (Core Logic)  │ ✅
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┬──────────────┼──────────────┬─────────────────┐
                       │                 │              │              │                 │
                ┌─────────────┐   ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
                │ Test Case   │   │   Script    │ │  Execution  │ │   Result    │ │  Feedback   │
                │ Generator   │✅ │ Generator   │🚧│  Scheduler  │🚧│  Analyzer   │🚧│ & Learning  │🚧
                └─────────────┘   └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘
                       │                 │              │              │                 │
                ┌─────────────┐   ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
                │ AI Service  │   │   Crypto    │ │ Environment │ │  Security   │ │ Knowledge   │
                │ (LLM APIs)  │✅ │  Service    │⚠️│  Manager    │🚧│  & Audit    │✅│    Base     │🚧
                └─────────────┘   └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘
```

**图例**: ✅ 已完成 | ⚠️ 部分完成 | 🚧 开发中

### 🔧 核心组件状态

#### ✅ **已实现组件 (70-90%)**
- **Orchestrator Service**: 异步编排服务，支持会话管理和任务调度
- **Test Case Generator**: AI驱动的测试用例生成，支持风险评估
- **AI Service**: 多提供商支持 (OpenAI/Azure/Ollama)，结构化输出
- **Security & Audit**: 输入验证、速率限制、安全审计

#### ⚠️ **部分实现组件 (30-60%)**
- **Crypto Service**: 统一加密接口，支持多算法（当前模拟实现）

#### 🚧 **开发中组件 (0-30%)**
- **Script Generator**: 多语言脚本生成框架
- **Execution Scheduler**: 分布式执行调度
- **Result Analyzer**: AI辅助结果分析
- **Environment Manager**: 多环境管理
- **Knowledge Base**: 持续学习系统

## 🛡️ 安全特性

- **输入验证**: XSS、SQL注入、代码执行防护
- **速率限制**: API调用频率控制
- **安全审计**: 请求审计和风险评估
- **配置验证**: 生产环境安全检查
- **线程安全**: 并发访问保护

## 技术栈

- **后端**: Python + FastAPI + asyncio
- **AI框架**: LangChain + LangGraph
- **加密模块**: Rust + PyO3
- **数据库**: PostgreSQL + Redis + 向量数据库
- **部署**: Kubernetes + Helm
- **监控**: Prometheus + Grafana

## 🚀 快速开始

### 环境要求

- Python 3.9+
- uv (包管理工具)
- Docker (可选，用于容器化部署)

### 安装依赖

```bash
# 使用uv创建虚拟环境
uv venv

# 激活虚拟环境 (Windows PowerShell)
.venv\Scripts\Activate.ps1

# 安装依赖
uv install
```

### 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
# 设置AI提供商和API密钥
AI_PROVIDER=openai  # 或 azure, ollama, mock
AI_API_KEY=your-api-key-here

# 安全配置
SECURITY_SECRET_KEY=your-secret-key-32-chars-min
SECURITY_CORS_ORIGINS=["http://localhost:3000"]
```

### 开发模式运行

```bash
# 启动Orchestrator服务 (默认端口8000)
uv run python -m src.orchestrator.main

# 验证服务状态
curl http://localhost:8000/health

# 查看API文档
# 浏览器访问: http://localhost:8000/docs
```

### 基本使用示例

```bash
# 创建会话
curl -X POST http://localhost:8000/api/v1/sessions

# 生成测试用例
curl -X POST http://localhost:8000/api/v1/test-cases/generate \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "your-session-id",
    "requirements": "测试用户登录接口",
    "test_types": ["functional", "security"],
    "risk_level": "medium"
  }'
```

## 项目结构

```
TestGenius/
├── docs/                   # 文档目录
├── src/                    # 源码目录
│   ├── orchestrator/       # 编排服务
│   ├── crypto/            # 加密服务
│   ├── test_case/         # 测试用例生成
│   ├── script_gen/        # 脚本生成
│   ├── scheduler/         # 执行调度
│   ├── analysis/          # 结果分析
│   ├── feedback/          # 反馈闭环
│   ├── security/          # 安全合规
│   └── common/            # 公共工具
├── tests/                 # 测试目录
├── templates/             # 模板目录
├── .github/              # CI/CD配置
└── deploy/               # 部署配置
```

## 📈 最近更新 (v0.1.0)

### ✅ 已修复的关键问题
- **内存泄漏防护**: 异步任务自动清理和跟踪
- **线程安全**: 会话管理并发访问保护
- **安全增强**: 输入验证、速率限制、安全审计
- **配置管理**: 环境变量支持和生产验证
- **API优化**: 结构化输入验证和错误处理

### 🔧 性能优化
- 缓存管理: TTL过期和大小限制
- 资源清理: 自动清理过期资源
- 异步优化: 改进的并发处理

## 🎯 开发路线图

### 🚀 即将发布 (v0.2.0)
- [ ] 数据持久化层 (PostgreSQL + Redis)
- [ ] 身份认证和授权系统
- [ ] 真实加密算法实现
- [ ] 监控和指标收集

### 📋 中期计划 (v0.3.0)
- [ ] 多语言脚本生成
- [ ] 分布式执行调度
- [ ] AI辅助结果分析
- [ ] CI/CD集成

### 🌟 长期愿景 (v1.0.0)
- [ ] 微服务架构重构
- [ ] 企业级多租户支持
- [ ] 高级AI特性和知识库
- [ ] 性能优化和扩展性

## 📚 文档

- [项目设计文档](TestGenius.md)
- [开发进度](DEVELOPMENT_PROGRESS.md)
- [安装状态](SETUP_STATUS.md)
- [开发规范](docs/project_rules.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交代码 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发规范
- 遵循 [Conventional Commits](https://conventionalcommits.org/) 规范
- 代码覆盖率要求 > 80%
- 通过所有安全检查和性能测试

## 📄 许可证

[MIT License](LICENSE) - 详见 LICENSE 文件