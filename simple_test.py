import sys
import os

print("Python版本:", sys.version)
print("当前目录:", os.getcwd())

# 添加当前目录到路径
sys.path.insert(0, '.')

try:
    print("尝试导入配置模块...")
    from src.common.config import settings
    print("✓ 配置模块导入成功")
    print("版本:", settings.version)
    print("环境:", settings.environment)
except Exception as e:
    print("✗ 配置模块导入失败:", str(e))
    import traceback
    traceback.print_exc()

print("\n项目基础设置验证完成！") 